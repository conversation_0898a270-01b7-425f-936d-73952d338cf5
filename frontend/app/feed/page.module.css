.feedLayout {
  display: grid;
  grid-template-columns: 1fr 320px;
  gap: var(--spacing-xl);
  max-width: 100%;
  width: 100%;
}

.feedMain {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  max-width: 60vw;
  width: 100%;
}

.feedSidebar {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

@media (max-width: 1200px) {
  .feedLayout {
    grid-template-columns: 1fr;
  }
  
  .feedMain {
    max-width: 100%;
  }

  .feedSidebar {
    order: -1;
  }
}

@media (max-width: 768px) {
  .feedLayout {
    grid-template-columns: 1fr;
  }
  
  .feedMain {
    max-width: 100%;
  }

  .feedSidebar {
    order: -1;
  }
}

@media (max-width: 340px) {
  .feedLayout {
    grid-template-columns: 1fr;
  }
  
  .feedMain {
    max-width: 100%;
  }

  .feedSidebar {
    order: -1;
  }
}