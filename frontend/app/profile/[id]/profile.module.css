.profileContainer {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.loadingSpinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid var(--primary-navy);
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.errorContainer {
  text-align: center;
  padding: 40px 20px;
}

.errorContainer h2 {
  color: var(--error-red);
  margin-bottom: 16px;
}

.backButton {
  background-color: var(--primary-navy);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 20px;
}

.privateProfileMessage {
  text-align: center;
  padding: 60px 20px;
  background-color: var(--light-gray);
  border-radius: 12px;
  margin-top: 20px;
}

.privateProfileMessage i {
  font-size: 48px;
  color: var(--text-light);
  margin-bottom: 16px;
}

.privateProfileMessage h3 {
  margin-bottom: 8px;
  color: var(--text-dark);
}

.privateProfileMessage p {
  color: var(--text-light);
}

.profileWelcome {
  background-color: var(--light-gray);
  border-radius: 12px;
  padding: 40px 20px;
  margin-top: 20px;
  text-align: center;
}

.welcomeContent h3 {
  color: var(--text-dark);
  margin-bottom: 8px;
  font-size: 24px;
  font-weight: 600;
}

.welcomeContent p {
  color: var(--text-light);
  margin-bottom: 32px;
  font-size: 16px;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
  max-width: 400px;
  margin: 0 auto;
}

.statItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.statNumber {
  font-size: 32px;
  font-weight: 700;
  color: var(--primary-navy);
}

.statLabel {
  font-size: 14px;
  color: var(--text-light);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

@media (max-width: 768px) {
  .profileWelcome {
    padding: 24px 16px;
    margin-top: 16px;
  }

  .statsGrid {
    gap: 24px;
    max-width: 300px;
  }

  .statNumber {
    font-size: 24px;
  }

  .welcomeContent h3 {
    font-size: 20px;
  }
}