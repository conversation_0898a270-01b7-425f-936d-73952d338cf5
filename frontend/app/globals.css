@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Modern Purple Theme */
  --primary-purple: #6366F1;
  --primary-purple-hover: #4F46E5;
  --primary-purple-light: #8B5CF6;
  --accent-purple: #A855F7;
  --accent-purple-light: #C084FC;
  --secondary-purple: #7C3AED;
  --purple-gradient: linear-gradient(135deg, #6366F1 0%, #8B5CF6 50%, #A855F7 100%);
  --purple-gradient-light: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 50%, rgba(168, 85, 247, 0.1) 100%);
  --primary-navy: var(--accent-purple);
  --secondary-navy: #4F46E5;
  
  /* Neutral Colors */
  --background: #FAFBFC;
  --card: #FFFFFF;
  --card-hover: #F8FAFC;
  --card-background: #eaecf1;
  --border: #E2E8F0;
  --border-light: #F1F5F9;
  --border-gray: #64748B;
  --text-primary: #1E293B;
  --text-secondary: #64748B;
  --text-muted: #94A3B8;
  --text-light: #1c385b;
  
  /* Status Colors */
  --success: #10B981;
  --success-light: #D1FAE5;
  --warning: #F59E0B;
  --warning-light: #FEF3C7;
  --error: #EF4444;
  --error-light: #FEE2E2;
  --info: #3B82F6;
  --info-light: #DBEAFE;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0,0,0,0.08);
  --shadow-md: 0 2px 4px rgba(0,0,0,0.12);
  --shadow-lg: 0 4px 8px rgba(0,0,0,0.15);
  --shadow-xl: 0 8px 16px rgba(0,0,0,0.15);
  
  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 24px;
  --spacing-2xl: 32px;
  
  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-full: 50%;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--background);
  color: var(--text-primary);
  line-height: 1.5;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Form Components */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
  font-size: 14px;
  color: var(--text-primary);
  line-height: 1.25;
}

.form-input {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg);
  border: 2px solid var(--border);
  border-radius: var(--radius-md);
  font-size: 16px;
  font-family: inherit;
  background: var(--card);
  color: var(--text-primary);
  transition: all 0.15s ease-in-out;
}

.form-input:focus {
  border-color: var(--primary-purple);
  outline: none;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-input::placeholder {
  color: var(--text-muted);
}

/* Card Components */
.card {
  background-color: var(--card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border);
  overflow: hidden;
  transition: all 0.15s ease-in-out;
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

.card-header {
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid var(--border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--card);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.25;
}

.card-body {
  padding: var(--spacing-lg) var(--spacing-xl);
}

/* Button Components */
.btn-primary {
  background: var(--purple-gradient);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: 16px;
  font-weight: 600;
  font-family: inherit;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: all 0.15s ease-in-out;
  min-height: 48px;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.4);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-purple);
  border: 2px solid var(--primary-purple);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: 16px;
  font-weight: 600;
  font-family: inherit;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: all 0.15s ease-in-out;
  min-height: 48px;
}

.btn-outline:hover {
  background: var(--purple-gradient);
  color: white;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--card-hover);
  color: var(--text-primary);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md) var(--spacing-2xl);
  font-size: 16px;
  font-weight: 600;
  font-family: inherit;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: all 0.15s ease-in-out;
  min-height: 48px;
}

.btn-secondary:hover {
  background-color: var(--border-light);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* Additional Form Styles */
.form-textarea {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  font-size: 14px;
  font-family: inherit;
  background: var(--card);
  color: var(--text-primary);
  transition: all 0.15s ease-in-out;
  resize: vertical;
  min-height: 80px;
}

.form-textarea:focus {
  border-color: var(--primary-purple);
  outline: none;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.1);
}

.form-textarea::placeholder {
  color: var(--text-muted);
}

/* Avatar Components */
.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  background: var(--purple-gradient);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
  overflow: hidden;
  flex-shrink: 0;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-avatar-sm {
  width: 32px;
  height: 32px;
  font-size: 14px;
}

.user-avatar-lg {
  width: 56px;
  height: 56px;
  font-size: 20px;
}

.friend-avatar {
  position: relative;
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  background: var(--purple-gradient);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  overflow: hidden;
  flex-shrink: 0;
}

.online-indicator {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  background-color: var(--success);
  border: 2px solid var(--card);
  border-radius: var(--radius-full);
}

/* Utility Classes */
.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background-color: var(--error);
  color: white;
  border-radius: var(--radius-full);
  min-width: 20px;
  height: 20px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--card);
  padding: 0 4px;
  z-index: 10;
}

/* Typography Utilities */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-muted {
  color: var(--text-muted);
}

.text-sm {
  font-size: 14px;
}

.text-xs {
  font-size: 12px;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

/* Layout Utilities */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-2 {
  gap: var(--spacing-sm);
}

.gap-3 {
  gap: var(--spacing-md);
}

.gap-4 {
  gap: var(--spacing-lg);
}

.mb-2 {
  margin-bottom: var(--spacing-sm);
}

.mb-3 {
  margin-bottom: var(--spacing-md);
}

.mb-4 {
  margin-bottom: var(--spacing-lg);
}

/* Hover States */
.hover-bg {
  transition: background-color 0.15s ease-in-out;
}

.hover-bg:hover {
  background-color: var(--card-hover);
}

/* Modern Effects */
.glassmorphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-text {
  background: var(--purple-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.floating-animation {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Interactive Elements */
.interactive {
  transition: all 0.2s ease-in-out;
}

.interactive:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Progress Bar */
.progress-bar {
  width: 100%;
  height: 8px;
  background-color: var(--border-light);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--purple-gradient);
  border-radius: var(--radius-full);
  transition: width 0.3s ease-in-out;
}

/* Responsive Design */
@media (max-width: 768px) {
  .card-body {
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .card-header {
    padding: var(--spacing-md) var(--spacing-lg);
  }
  
  .btn-primary,
  .btn-outline,
  .btn-secondary {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: 14px;
  }
}
