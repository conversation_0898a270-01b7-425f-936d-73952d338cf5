.chatContainer {
  display: flex;
  flex-direction: column;
  /* Set a fixed height relative to the viewport.
     This assumes a header/navbar and padding take up ~100px.
     This is the key to containing the layout and enabling internal scrolling. */
  height: calc(100vh - 100px);
  width: 100%;
  background-color: var(--white);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--shadow);
}

.connectionStatus,
.connectionError {
  padding: 10px 20px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--white);
}

.connectionStatus {
  background-color: var(--warning);
}

.connectionError {
  background-color: var(--danger);
}

.chatLayout {
  display: grid;
  grid-template-columns: 300px 1fr;
  flex: 1; /* Allow it to grow and fill the remaining space in the container */
  min-height: 0; /* A crucial fix for flexbox children that need to scroll */
  overflow: hidden; /* Ensure the grid itself does not scroll */
}
