.loginContainer {
  min-height: 100vh;
  display: grid;
  grid-template-columns: 1fr 1fr;
  background: var(--background);
}

.loginHero {
  background: var(--purple-gradient);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-2xl);
  position: relative;
  overflow: hidden;
}

.loginHero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  animation: float 20s linear infinite;
}

.heroContent {
  text-align: center;
  color: white;
  z-index: 2;
  position: relative;
}

.heroTitle {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: var(--spacing-lg);
  line-height: 1.1;
}

.heroSubtitle {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-2xl);
  opacity: 0.9;
  line-height: 1.5;
  max-width: 400px;
}

.heroFeatures {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  max-width: 350px;
}

.heroFeature {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  text-align: left;
}

.featureIcon {
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.featureText {
  font-size: 1rem;
  opacity: 0.9;
}

.loginFormSection {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  background: var(--card);
}

.loginCard {
  width: 100%;
  max-width: 400px;
  background: transparent;
  border-radius: 0;
  box-shadow: none;
}

.loginHeader {
  background: transparent;
  color: var(--text-primary);
  padding: 0 0 var(--spacing-2xl) 0;
  text-align: center;
}

.loginTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: var(--spacing-sm);
  background: var(--purple-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.loginSubtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
}

.registerCard {
  max-width: 600px;
  background-color: var(--card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
}

@media (max-width: 768px) {
  .loginContainer {
    grid-template-columns: 1fr;
  }
  
  .loginHero {
    padding: var(--spacing-xl);
    min-height: 40vh;
  }
  
  .heroTitle {
    font-size: 2.5rem;
  }
  
  .heroSubtitle {
    font-size: 1rem;
  }
  
  .loginFormSection {
    padding: var(--spacing-xl);
  }
  
  .loginTitle {
    font-size: 2rem;
  }
  
  .registerCard {
    max-width: 100%;
  }
}
