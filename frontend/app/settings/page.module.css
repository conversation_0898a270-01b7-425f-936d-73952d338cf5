.settingsContainer {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.settingsHeader {
  margin-bottom: 30px;
}

.settingsHeader h1 {
  margin: 0 0 5px 0;
  color: var(--primary-navy);
  font-size: 28px;
  font-weight: 600;
}

.settingsHeader p {
  margin: 0;
  color: var(--text-light);
  font-size: 16px;
}

.successMessage,
.errorMessage {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-weight: 500;
}

.successMessage {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.errorMessage {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.settingsLayout {
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: 30px;
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: var(--shadow);
  overflow: hidden;
}

.settingsNav {
  background-color: var(--light-gray);
  padding: 20px 0;
  display: flex;
  flex-direction: column;
}

.navItem {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s;
  color: var(--text-dark);
  font-weight: 500;
}

.navItem:hover {
  background-color: var(--white);
}

.navItem.active {
  background-color: var(--primary-navy);
  color: var(--white);
}

.navItem i {
  width: 16px;
  text-align: center;
}

.settingsContent {
  padding: 30px;
}

.settingsSection h2 {
  margin: 0 0 8px 0;
  color: var(--primary-navy);
  font-size: 22px;
  font-weight: 600;
}

.settingsSection > p {
  margin: 0 0 25px 0;
  color: var(--text-light);
  font-size: 14px;
}

.settingsForm {
  max-width: 500px;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 20px;
}

.formGroup {
  margin-bottom: 20px;
}

.formGroup label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: var(--primary-navy);
  font-size: 14px;
}

.formGroup input,
.formGroup textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border-gray);
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.formGroup input:focus,
.formGroup textarea:focus {
  outline: none;
  border-color: var(--primary-navy);
}

.formGroup textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.privacyOption,
.notificationOption {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border: 1px solid var(--border-gray);
  border-radius: 8px;
  margin-bottom: 15px;
}

.optionInfo h3 {
  margin: 0 0 5px 0;
  color: var(--primary-navy);
  font-size: 16px;
  font-weight: 600;
}

.optionInfo p {
  margin: 0;
  color: var(--text-light);
  font-size: 14px;
}

/* Toggle Switch */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--primary-navy);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* Danger Zone */
.dangerZone {
  border: 2px solid #e74c3c;
  border-radius: 8px;
  padding: 20px;
  background-color: #fdf2f2;
}

.dangerAction {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dangerInfo h3 {
  margin: 0 0 5px 0;
  color: #e74c3c;
  font-size: 16px;
  font-weight: 600;
}

.dangerInfo p {
  margin: 0;
  color: #721c24;
  font-size: 14px;
}

.dangerButton {
  background-color: #e74c3c;
  color: var(--white);
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.2s;
}

.dangerButton:hover {
  background-color: #c0392b;
}

.dangerButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .settingsContainer {
    padding: 15px;
  }
  
  .settingsLayout {
    grid-template-columns: 1fr;
    gap: 0;
  }
  
  .settingsNav {
    flex-direction: row;
    overflow-x: auto;
    padding: 15px 0;
  }
  
  .navItem {
    white-space: nowrap;
    padding: 8px 15px;
    flex-shrink: 0;
  }
  
  .settingsContent {
    padding: 20px;
  }
  
  .formRow {
    grid-template-columns: 1fr;
    gap: 0;
  }
  
  .settingsForm {
    max-width: none;
  }
  
  .privacyOption,
  .notificationOption {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .dangerAction {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .dangerButton {
    width: 100%;
  }
}