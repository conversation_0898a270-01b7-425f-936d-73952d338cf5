.searchContainer {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.searchHeader {
  margin-bottom: 30px;
}

.searchForm {
  margin-bottom: 20px;
}

.searchInputContainer {
  position: relative;
  display: flex;
  align-items: center;
  max-width: 600px;
  margin: 0 auto;
}

.searchInputContainer i {
  position: absolute;
  left: 15px;
  color: var(--text-light);
  z-index: 1;
}

.searchInput {
  flex: 1;
  padding: 15px 20px 15px 45px;
  border: 2px solid var(--border-gray);
  border-radius: 25px;
  font-size: 16px;
  transition: border-color 0.2s;
}

.searchInput:focus {
  outline: none;
  border-color: var(--primary-navy);
}

.searchButton {
  margin-left: 10px;
  padding: 15px 25px;
  background-color: var(--primary-navy);
  color: var(--white);
  border: none;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.searchButton:hover {
  background-color: var(--secondary-navy);
}

.searchInfo h2 {
  margin: 0 0 5px 0;
  color: var(--primary-navy);
  font-size: 24px;
  font-weight: 600;
}

.searchInfo p {
  margin: 0;
  color: var(--text-light);
  font-size: 14px;
}

.searchTabs {
  display: flex;
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: var(--shadow);
  margin-bottom: 20px;
  overflow: hidden;
}

.tab {
  flex: 1;
  padding: 15px 20px;
  background: none;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: 500;
  color: var(--text-light);
}

.tab:hover {
  background-color: var(--light-gray);
}

.tab.active {
  background-color: var(--primary-navy);
  color: var(--white);
}

.tab:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.searchResults {
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: var(--shadow);
  overflow: hidden;
}

.usersResults,
.groupsResults,
.postsResults {
  padding: 20px;
}

.userCard,
.groupCard {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid var(--border-gray);
  transition: background-color 0.2s;
}

.userCard:last-child,
.groupCard:last-child {
  border-bottom: none;
}

.userCard:hover,
.groupCard:hover {
  background-color: var(--light-gray);
}

.userAvatar,
.groupAvatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: var(--light-gray);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-weight: 600;
  color: var(--primary-navy);
  overflow: hidden;
}

.userAvatar img,
.groupAvatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.userInfo,
.groupInfo {
  flex: 1;
  margin-right: 15px;
}

.avatarLink {
  text-decoration: none;
  transition: opacity 0.2s;
}

.avatarLink:hover {
  opacity: 0.8;
}

.userNameLink {
  text-decoration: none;
  color: inherit;
}

.userNameLink:hover h3 {
  color: var(--primary-purple, #8b5cf6);
}

.userInfo h3,
.groupInfo h3 {
  margin: 0 0 5px 0;
  color: var(--primary-navy);
  font-size: 16px;
  font-weight: 600;
  transition: color 0.2s;
}

.userInfo p,
.groupInfo p {
  margin: 0 0 3px 0;
  color: var(--text-light);
  font-size: 14px;
}

.userInfo span,
.groupInfo span {
  color: var(--text-dark);
  font-size: 13px;
  display: block;
  margin-top: 5px;
  line-height: 1.4;
}

.userActions,
.groupActions {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
}

.followButton {
  background-color: var(--primary-purple, #8b5cf6);
  color: white;
  border: 2px solid var(--primary-purple, #8b5cf6);
  border-radius: 8px;
  padding: 8px 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease-in-out;
  font-size: 14px;
  min-width: 100px;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.followButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.followButton:hover::before {
  left: 100%;
}

.followButton:hover {
  background-color: var(--accent-purple, #7c3aed);
  border-color: var(--accent-purple, #7c3aed);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.3);
}

.followButton:active {
  transform: translateY(0);
  box-shadow: 0 3px 10px rgba(139, 92, 246, 0.2);
}

.followButton.following {
  background-color: var(--card-hover, #f8fafc);
  color: var(--primary-purple, #8b5cf6);
  border-color: var(--primary-purple, #8b5cf6);
}

.followButton.following:hover {
  background-color: var(--background-secondary, #f1f5f9);
  border-color: var(--accent-purple, #7c3aed);
  color: var(--accent-purple, #7c3aed);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.2);
}

.followButton.pending {
  background-color: var(--warning-light, #fef3c7);
  color: var(--warning-dark, #92400e);
  border-color: var(--warning, #f59e0b);
  cursor: default;
}

.followButton.pending:hover {
  background-color: var(--warning-light, #fef3c7);
  color: var(--warning-dark, #92400e);
  border-color: var(--warning, #f59e0b);
  transform: none;
  box-shadow: none;
}

.followButton.pending::before {
  display: none;
}

.followButton:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

.followButton:disabled:hover {
  transform: none;
  box-shadow: none;
}

.userActions button,
.groupActions button {
  padding: 8px 16px;
  font-size: 13px;
  white-space: nowrap;
}

.loadingState,
.errorState,
.emptyResults,
.noSearchState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: var(--text-light);
  gap: 15px;
}

.loadingState i,
.errorState i,
.emptyResults i,
.noSearchState i {
  font-size: 48px;
  color: var(--border-gray);
  margin-bottom: 10px;
}

.errorState i {
  color: #e74c3c;
}

.errorState p,
.emptyResults p,
.noSearchState p {
  margin: 0;
  color: var(--text-dark);
}

.noSearchState h2 {
  margin: 0;
  color: var(--primary-navy);
  font-size: 24px;
  font-weight: 600;
}

.errorState button {
  margin-top: 10px;
}

@media (max-width: 768px) {
  .searchContainer {
    padding: 15px;
  }
  
  .searchInputContainer {
    flex-direction: column;
    gap: 10px;
  }
  
  .searchInput {
    width: 100%;
  }
  
  .searchButton {
    margin-left: 0;
    width: 100%;
  }
  
  .searchTabs {
    flex-direction: column;
  }
  
  .tab {
    padding: 12px 15px;
  }
  
  .userCard,
  .groupCard {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    padding: 20px 15px;
  }
  
  .userAvatar,
  .groupAvatar {
    margin-right: 0;
    align-self: center;
  }
  
  .userInfo,
  .groupInfo {
    margin-right: 0;
    text-align: center;
    width: 100%;
  }
  
  .userActions,
  .groupActions {
    width: 100%;
    justify-content: center;
  }
  
  .userActions button,
  .groupActions button {
    flex: 1;
  }
}

/* Post card styles */
.postCard {
  border-bottom: 1px solid var(--border-gray);
  padding: 20px 15px;
  transition: background-color 0.2s;
}

.postCard:last-child {
  border-bottom: none;
}

.postCard:hover {
  background-color: var(--light-gray);
}

.postHeader {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.postInfo {
  margin-left: 12px;
  flex: 1;
}

.authorLink {
  text-decoration: none;
  color: inherit;
}

.authorLink:hover h4 {
  color: var(--primary-purple, #8b5cf6);
}

.postInfo h4 {
  margin: 0 0 2px 0;
  color: var(--primary-navy);
  font-size: 16px;
  font-weight: 600;
  transition: color 0.2s;
}

.postInfo span {
  color: var(--text-light);
  font-size: 14px;
  margin-right: 8px;
}

.postInfo time {
  color: var(--text-light);
  font-size: 13px;
}

.postContent {
  margin-bottom: 15px;
}

.postContent p {
  margin: 0 0 10px 0;
  color: var(--text-dark);
  line-height: 1.5;
  font-size: 15px;
}

.postImage {
  width: 100%;
  border-radius: var(--radius-lg, 12px);
  margin-top: 10px;
  margin-bottom: 15px;
  background-color: var(--background, #f8fafc);
  border: 1px solid var(--border, #e2e8f0);
  overflow: hidden;
  transition: all 0.15s ease-in-out;
  cursor: pointer;
  max-height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.postImage:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg, 0 10px 25px rgba(0, 0, 0, 0.1));
}

.postImage img {
  width: auto;
  height: auto;
  display: block;
  max-width: 100%;
  max-height: 500px;
  object-fit: contain;
  background: var(--white, #ffffff);
  transition: transform 0.2s ease;
  border-radius: 6px;
}

/* Ensure images don't get too small on very wide screens */
@media (min-width: 1200px) {
  .postImage img {
    min-width: 300px;
  }
}

.postImage:hover img {
  transform: scale(1.02);
}

.postStats {
  display: flex;
  gap: 20px;
  color: var(--text-light);
  font-size: 14px;
}

.postStats span {
  display: flex;
  align-items: center;
  gap: 6px;
}

.postStats i {
  font-size: 13px;
}

@media (max-width: 768px) {
  .postCard {
    padding: 15px 10px;
  }

  .postHeader {
    margin-bottom: 12px;
  }

  .postContent {
    margin-bottom: 12px;
  }

  .postImage {
    max-height: 400px;
  }

  .postImage img {
    max-height: 400px;
    max-width: 100%;
  }

  .postStats {
    gap: 15px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .postImage {
    max-height: 300px;
  }

  .postImage img {
    max-height: 300px;
    max-width: 100%;
  }
}

@media (max-width: 320px) {
  .postImage {
    max-height: 250px;
  }

  .postImage img {
    max-height: 250px;
  }
}