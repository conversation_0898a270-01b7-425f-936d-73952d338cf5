.chatMain {
  display: flex;
  flex-direction: column;
  /* Ensure the main chat area fills the grid cell and can contain a scrolling child */
  height: 100%;
  min-height: 0;
}

.noConversation {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  font-size: 16px;
  gap: 10px;
}

.noConversation i {
  font-size: 48px;
  color: var(--border-gray);
  margin-bottom: 10px;
}

.noConversation h3 {
  margin: 0;
  color: var(--primary-navy);
}

.noConversation p {
  margin: 0;
  font-size: 14px;
}

.emptyMessages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--text-light);
  gap: 10px;
}

.emptyMessages i {
  font-size: 32px;
  color: var(--border-gray);
}

.chatHeader {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: var(--card);
}

.chatHeaderName {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 16px;
}

.chatHeaderStatus {
  font-size: 12px;
  color: var(--text-secondary);
}

.chatHeaderActions {
  margin-left: auto;
  display: flex;
  gap: var(--spacing-md);
}

.chatHeaderActions i {
  cursor: pointer;
  padding: var(--spacing-sm);
  color: var(--text-secondary);
  transition: all 0.15s ease-in-out;
  border-radius: var(--radius-full);
}

.chatHeaderActions i:hover {
  color: var(--primary-purple);
  background-color: var(--purple-gradient-light);
  transform: scale(1.1);
}

.chatMessages {
  flex: 1;
  padding: var(--spacing-lg);
  overflow-y: auto;
  background-color: var(--background);
}

.message {
  display: flex;
  margin-bottom: var(--spacing-md);
  align-items: flex-start;
}

.message.own {
  justify-content: flex-end;
}

.messageBubble {
  min-width: 60px;
  max-width: 70%;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-lg);
  font-size: 14px;
  line-height: 1.4;
  transition: all 0.15s ease-in-out;
  word-break: break-word;
  white-space: pre-wrap;
}

.message.own .messageBubble {
  background: var(--purple-gradient);
  color: white;
  border-bottom-right-radius: var(--radius-sm);
  box-shadow: var(--shadow-sm);
}

.message:not(.own) .messageBubble {
  background-color: var(--card);
  color: var(--text-primary);
  border-bottom-left-radius: var(--radius-sm);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border);
}

.messageTime {
  font-size: 11px;
  color: var(--text-muted);
  margin-top: var(--spacing-xs);
  text-align: right;
}

.message:not(.own) .messageTime {
  text-align: left;
}

.chatInput {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border);
  background-color: var(--card);
}

.chatInputContainer {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.chatInputContainer input {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--border);
  border-radius: var(--radius-full);
  font-size: 14px;
  background-color: var(--background);
  color: var(--text-primary);
  transition: all 0.15s ease-in-out;
}

.chatInputContainer input:focus {
  outline: none;
  border-color: var(--primary-purple);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.chatInputContainer i {
  cursor: pointer;
  color: var(--text-secondary);
  padding: var(--spacing-sm);
  border-radius: var(--radius-full);
  transition: all 0.15s ease-in-out;
}

.chatInputContainer i:hover {
  color: var(--primary-purple);
  background-color: var(--purple-gradient-light);
}

.chatSendBtn {
  background: var(--purple-gradient);
  color: white;
  border: none;
  border-radius: var(--radius-full);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  box-shadow: var(--shadow-sm);
}

.chatSendBtn:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

.chatSendBtn:disabled {
  background-color: var(--border-gray);
  cursor: not-allowed;
}

.messageSender {
  font-size: 12px;
  font-weight: 600;
  color: #667eea;
  margin-bottom: 4px;
}

.messageContent {
  padding: 12px 16px;
  border-radius: 18px;
  word-wrap: break-word;
  position: relative;
  display: inline-block;
  max-width: 100%;
}

.message.own .messageContent {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.message:not(.own) .messageContent {
  background: #f8fafc;
}

.pendingIndicator {
  margin-left: 8px;
  opacity: 0.7;
  font-size: 12px;
}

.groupInfoOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.inputWrapper {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: white;
  border-top: 1px solid #e1e5e9;
}

.messageInput {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 24px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.messageInput:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.sendButton {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.sendButton:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.sendButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.readStatus {
  margin-left: 4px;
  font-size: 10px;
}

.readStatus.fa-check-double {
  color: var(--primary-blue);
}

/* Typing indicator styles */
.typingIndicator {
  display: flex;
  align-items: flex-end;
  margin-bottom: 12px;
  gap: 8px;
}

.typingBubble {
  background-color: var(--white);
  border-radius: 16px;
  border-bottom-left-radius: 4px;
  padding: 10px 14px;
  box-shadow: var(--shadow);
  display: flex;
  align-items: center;
  min-height: 20px;
}

.typingDots {
  display: flex;
  gap: 3px;
}

.typingDots span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--text-light);
  animation: typingAnimation 1.4s infinite ease-in-out;
}

.typingDots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typingDots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typingAnimation {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.typingText {
  font-size: 11px;
  color: var(--text-light);
  align-self: flex-end;
  margin-bottom: 2px;
}

/* Image message styles */
.messageImage {
  margin-bottom: 4px;
  border-radius: 12px;
  overflow: hidden;
  max-width: 200px;
}

.messageImage img {
  width: 100%;
  height: auto;
  display: block;
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.messageImage img:hover {
  opacity: 0.9;
}

.messageText {
  word-wrap: break-word;
}

/* Image preview styles */
.imagePreviewContainer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border);
  background-color: var(--background);
}

.imagePreview {
  position: relative;
  display: inline-block;
  max-width: 150px;
  border-radius: 8px;
  overflow: hidden;
}

.imagePreview img {
  width: 100%;
  height: auto;
  display: block;
}

.removeImageBtn {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 10px;
  transition: background 0.2s ease;
}

.removeImageBtn:hover {
  background: rgba(0, 0, 0, 0.8);
}

/* Attachment button styles */
.attachmentBtn {
  background: none;
  border: none;
  color: var(--text-secondary);
  padding: var(--spacing-sm);
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
}

.attachmentBtn:hover {
  color: var(--primary-purple);
  background-color: var(--purple-gradient-light);
}
