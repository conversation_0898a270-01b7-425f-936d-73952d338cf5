.chatSidebar {
  background-color: var(--background);
  border-right: 1px solid var(--border);
  display: flex;
  flex-direction: column;
  /* Ensure the sidebar fills the grid cell and can contain a scrolling child */
  height: 100%;
  min-height: 0;
}

.chatSearch {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border);
  background-color: var(--card);
  position: relative;
}

.chatSearch i {
  position: absolute;
  left: 25px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light);
  font-size: 12px;
}

.chatSearch input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-xl);
  border: 2px solid var(--border);
  border-radius: var(--radius-md);
  font-size: 14px;
  background-color: var(--background);
  color: var(--text-primary);
  transition: all 0.15s ease-in-out;
}

.chatSearch input:focus {
  outline: none;
  border-color: var(--primary-purple);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.chatSearch input:disabled {
  background-color: var(--light-gray);
  cursor: not-allowed;
}

.chatList {
  flex: 1;
  overflow-y: auto;
}

.chatItem {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  border-bottom: 1px solid var(--border);
}

.chatItem:hover {
  background-color: var(--card-hover);
  transform: translateX(4px);
}

.chatItem.active {
  background-color: var(--purple-gradient-light);
  border-right: 3px solid var(--primary-purple);
}

.chatItemInfo {
  flex: 1;
}

.chatItemName {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.chatItemPreview {
  font-size: 12px;
  color: var(--text-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chatItemTime {
  font-size: 11px;
  color: var(--text-muted);
}

.chatItemMeta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.chatItemMeta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.unreadBadge {
  background: var(--purple-gradient);
  color: white;
  border-radius: var(--radius-full);
  width: 20px;
  height: 20px;
  font-size: 11px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: var(--spacing-xs);
  box-shadow: var(--shadow-sm);
  font-weight: 600;
}

.groupIndicator {
  margin-left: 4px;
  color: var(--text-light);
  font-size: 11px;
}

.loadingState,
.errorState,
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: var(--text-light);
  gap: 10px;
}

.loadingState i,
.errorState i,
.emptyState i {
  font-size: 32px;
  color: var(--border-gray);
  margin-bottom: 10px;
}

.errorState button {
  margin-top: 10px;
  padding: 8px 16px;
  font-size: 12px;
}

.emptyState p {
  margin: 0;
  font-weight: 500;
  color: var(--primary-navy);
}

.emptyState span {
  margin: 0;
  font-size: 12px;
}
