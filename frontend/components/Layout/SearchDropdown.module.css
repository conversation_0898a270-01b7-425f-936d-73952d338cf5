.searchDropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  z-index: 1000;
  max-height: 400px;
  overflow: hidden;
  margin-top: 4px;
}

.loadingState,
.errorState,
.hintState,
.emptyState {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  color: var(--text-secondary);
  font-size: 14px;
}

.loadingState i {
  color: var(--primary-purple);
}

.errorState {
  color: var(--error);
}

.errorState i {
  color: var(--error);
}

.hintState i {
  color: var(--text-muted);
}

.emptyState i {
  color: var(--text-muted);
}

.resultsContainer {
  max-height: 350px;
  overflow-y: auto;
}

.resultsHeader {
  padding: var(--spacing-sm) var(--spacing-lg);
  background: var(--background);
  border-bottom: 1px solid var(--border);
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.resultsList {
  padding: var(--spacing-xs) 0;
}

.userResult {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-sm) var(--spacing-lg);
  text-decoration: none;
  color: var(--text-primary);
  transition: all 0.15s ease-in-out;
  cursor: pointer;
}

.userResult:hover,
.userResult.selected {
  background: var(--card-hover);
  color: var(--primary-purple);
}

.userResult.selected {
  background: var(--primary-purple);
  color: white;
}

.userResult.selected .userName,
.userResult.selected .userUsername {
  color: white;
}

.userAvatar {
  flex-shrink: 0;
}

.userInfo {
  flex: 1;
  min-width: 0;
}

.userName {
  font-weight: 600;
  font-size: 14px;
  line-height: 1.2;
  margin-bottom: 2px;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.userUsername {
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Scrollbar styling */
.resultsContainer::-webkit-scrollbar {
  width: 6px;
}

.resultsContainer::-webkit-scrollbar-track {
  background: var(--background);
}

.resultsContainer::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 3px;
}

.resultsContainer::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Responsive design */
@media (max-width: 768px) {
  .searchDropdown {
    left: -20px;
    right: -20px;
    max-height: 300px;
  }
  
  .userResult {
    padding: var(--spacing-md) var(--spacing-lg);
  }
  
  .userName {
    font-size: 15px;
  }
  
  .userUsername {
    font-size: 13px;
  }
  
  .loadingState,
  .errorState,
  .hintState,
  .emptyState {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: 15px;
  }
}
