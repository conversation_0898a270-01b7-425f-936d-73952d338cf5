.sidebar {
  width: 12vw;
  background-color: var(--card);
  border-right: 1px solid var(--border);
  padding: var(--spacing-xl) 0;
  height: calc(100vh - 52px);
  position: fixed;
  left: calc(50% - 39%);
  overflow-y: auto;
  top: 52px;
  backdrop-filter: blur(10px);
}

.sidebarSection {
  margin-bottom: var(--spacing-2xl);
}

.sidebarTitle {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  color: var(--text-muted);
  margin-bottom: var(--spacing-md);
  padding: 0 var(--spacing-xl);
  letter-spacing: 0.5px;
}

.sidebarItem {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-xl);
  margin: 0 var(--spacing-md);
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: all 0.15s ease-in-out;
  font-weight: 500;
  cursor: pointer;
  border: none;
  background: none;
  width: calc(100% - var(--spacing-xl));
  font-size: 14px;
  line-height: 1.25;
}

.sidebarItem:hover {
  background-color: var(--card-hover);
  color: var(--primary-purple);
  transform: translateX(4px);
  box-shadow: var(--shadow-sm);
}

.sidebarItem:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.sidebarItem i {
  margin-right: var(--spacing-md);
  width: 20px;
  font-size: 18px;
  text-align: center;
  flex-shrink: 0;
}

.sidebarItem.active {
  background: var(--purple-gradient);
  color: white;
  box-shadow: var(--shadow-md);
}

.badge {
  margin-left: auto;
  background-color: var(--error);
  color: white;
  border-radius: var(--radius-full);
  padding: 2px var(--spacing-sm);
  font-size: 11px;
  font-weight: 600;
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
}

@media (max-width: 1200px) {
  .sidebar {
    left: 0;
    width: 20vw;
  }
  
  .sidebarItem {
    padding: var(--spacing-sm) var(--spacing-md);
    margin: 0 var(--spacing-sm);
    font-size: 13px;
    width: calc(100% - var(--spacing-md));
  }

  .sidebarSection {
    margin-bottom: var(--spacing-xl);
  }
}

@media (max-width: 820px) {
  .sidebar {
    left: 0;
    width: 20vw;
    padding: var(--spacing-lg) 0;
  }

  .sidebarItem {
    padding: var(--spacing-sm) var(--spacing-md);
    margin: 0 var(--spacing-sm);
    font-size: 13px;
    width: calc(100% - var(--spacing-md));
  }

  .sidebarItem i {
    margin-right: var(--spacing-sm);
    font-size: 16px;
    width: 16px;
  }

  .sidebarTitle {
    padding: 0 var(--spacing-md);
    font-size: 11px;
  }
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s;
    left: 0;
  }

  .sidebar.mobileOpen {
    transform: translateX(0);
  }
}
