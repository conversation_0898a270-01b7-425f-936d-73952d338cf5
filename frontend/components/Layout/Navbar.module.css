.navbar {
  background-color: var(--card);
  color: var(--text-primary);
  padding: 0;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: var(--shadow-md);
  border-bottom: 1px solid var(--border);
  backdrop-filter: blur(10px);
}

.navContainer {
  max-width: 80vw;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-xl);
  height: 52px;
}

.navBrand {
  font-size: 24px;
  font-weight: 700;
  background: var(--purple-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-decoration: none;
  letter-spacing: -0.5px;
}

.navSearch {
  flex: 1;
  max-width: 350px;
  margin: 0 30px;
  position: relative;
  z-index: 100;
}

.navSearch input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-2xl) var(--spacing-sm) var(--spacing-lg);
  border: 2px solid var(--border);
  border-radius: var(--radius-lg);
  background-color: var(--background);
  color: var(--text-primary);
  font-size: 14px;
  font-family: inherit;
  transition: all 0.15s ease-in-out;
}

.navSearch input::placeholder {
  color: var(--text-muted);
}

.navSearch input:focus {
  border-color: var(--primary-purple);
  outline: none;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  background-color: var(--card);
}

.navSearch i {
  position: absolute;
  right: var(--spacing-lg);
  top: 50%;
  transform: translateY(-50%);
  color: var(--linkedin-text-muted);
}

.navActions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.navIcon {
  position: relative;
  padding: var(--spacing-sm);
  border-radius: var(--radius-lg);
  background-color: transparent;
  color: var(--text-secondary);
  font-size: 20px;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.navIcon:hover {
  background-color: var(--card-hover);
  color: var(--primary-purple);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.chatIcon {
  position: relative;
}

.groupChatIcon {
  position: relative;
}

.connectionWarning {
  position: absolute;
  top: -2px;
  right: -2px;
  font-size: 10px;
  color: #f39c12;
  background-color: var(--primary-navy);
  border-radius: 50%;
  padding: 2px;
}

.profileAvatar {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  background: var(--purple-gradient);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.15s ease-in-out;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  text-decoration: none;
}

.profileAvatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: var(--radius-full);
}

.profileAvatar:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.notificationPanel {
  position: fixed;
  top: 52px;
  right: var(--spacing-lg);
  width: 320px;
  background-color: var(--card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border);
  z-index: 1000;
  max-height: 400px;
  overflow-y: auto;
  backdrop-filter: blur(10px);
}

.notificationItem {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--linkedin-border);
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
}

.notificationItem:hover {
  background-color: var(--card-hover);
}

.notificationItem:last-child {
  border-bottom: none;
}

.notificationContent {
  font-size: 14px;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  line-height: 1.33;
}

.notificationTime {
  font-size: 12px;
  color: var(--text-muted);
}

@media (max-width: 768px) {
  .navContainer {
    padding: 0 var(--spacing-lg);
  }

  .navSearch {
    max-width: 200px;
    margin: 0 15px;
  }

  .navSearch input {
    font-size: 13px;
    padding: var(--spacing-xs) var(--spacing-lg) var(--spacing-xs) var(--spacing-md);
  }

  .navActions {
    gap: 8px;
  }

  .navIcon {
    width: 36px;
    height: 36px;
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .navSearch {
    max-width: 150px;
    margin: 0 10px;
  }

  .navSearch input {
    font-size: 12px;
    padding: 6px 30px 6px 10px;
  }

  .navSearch i {
    right: 8px;
    font-size: 12px;
  }
}
