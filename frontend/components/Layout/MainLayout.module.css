.appContainer {
  min-height: 100vh;
  max-width: 80vw;
  margin: auto;
}

.mainContainer {
  margin-top: 52px;
  min-height: calc(100vh - 52px);
  display: flex;
  background-color: var(--background);
  max-width: 80vw;
  margin-left: auto;
  margin-right: auto;
  position: relative;
}

.mainContent {
  margin-left: 13.2vw;
  padding: var(--spacing-xl);
  flex: 1;
  max-width: calc(80vw - 10vw);
  background-color: var(--background);
}

@media (max-width: 1200px) {
  .appContainer {
    max-width: 100vw;
  }

  .mainContainer {
    max-width: 100vw;
    margin: 52px 0 0 0;
  }

  .mainContent {
    margin-left: 21vw;
    max-width: calc(100vw - 20vw);
  }
}

@media (max-width: 820px) {
  .appContainer {
    max-width: 100vw;
  }

  .mainContainer {
    max-width: 100vw;
  }

  .mainContent {
    margin-left: 20vw;
    max-width: calc(100vw - 18vw);
    padding: var(--spacing-lg);
  }
}

@media (max-width: 768px) {
  .appContainer {
    max-width: 100vw;
  }

  .mainContainer {
    max-width: 100vw;
  }

  .mainContent {
    margin-left: 0;
    max-width: 100vw;
    padding: var(--spacing-lg);
  }
}

@media (max-width: 340px) {
  .appContainer {
    max-width: 100vw;
  }

  .mainContainer {
    max-width: 100vw;
    margin: auto;
  }

  .mainContent {
    margin-left: 0;
    max-width: 100vw;
    padding: var(--spacing-lg);
  }
}
