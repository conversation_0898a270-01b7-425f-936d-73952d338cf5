.postsContainer {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.loadingSpinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid var(--primary-navy);
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.errorContainer {
  text-align: center;
  padding: 30px;
  background-color: rgba(220, 53, 69, 0.1);
  border-radius: 12px;
}

.retryButton {
  background-color: var(--primary-navy);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  margin-top: 16px;
}

.emptyContainer {
  text-align: center;
  padding: 60px 20px;
  background-color: var(--light-gray);
  border-radius: 12px;
}

.emptyContainer i {
  font-size: 48px;
  color: var(--text-light);
  margin-bottom: 16px;
}

.emptyContainer h3 {
  margin-bottom: 8px;
  color: var(--text-dark);
}

.emptyContainer p {
  color: var(--text-light);
}

.loadMoreContainer {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

.loadMoreButton {
  background-color: var(--light-gray);
  color: var(--text-dark);
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s;
}

.loadMoreButton:hover {
  background-color: var(--border-gray);
}

.loadMoreButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}