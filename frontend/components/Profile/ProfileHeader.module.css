.profileHeader {
  background-color: var(--card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border);
  overflow: hidden;
  margin-bottom: var(--spacing-xl);
  transition: all 0.15s ease-in-out;
}

.profileHeader:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.profileCover {
  height: 200px;
  background: var(--purple-gradient);
  position: relative;
}

.profileCover:hover .coverEditOverlay {
  opacity: 1;
}

.coverImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.coverPlaceholder {
  width: 100%;
  height: 100%;
  background: var(--purple-gradient);
}

.coverEditOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.15s ease-in-out;
}

.coverEditButton {
  background-color: var(--card);
  color: var(--text-primary);
  border: 1px solid var(--border);
  border-radius: var(--radius-full);
  width: 48px;
  height: 48px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  transition: all 0.15s ease-in-out;
  box-shadow: var(--shadow-md);
}

.coverEditButton:hover {
  background-color: var(--card-hover);
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
  color: var(--primary-purple);
}

.profileAvatar {
  position: absolute;
  bottom: -60px;
  left: var(--spacing-2xl);
  width: 120px;
  height: 120px;
  border-radius: var(--radius-full);
  border: 4px solid var(--card);
  overflow: hidden;
  background: var(--purple-gradient);
  box-shadow: var(--shadow-lg);
}

.profileAvatar:hover .avatarEditButton {
  opacity: 1;
}

.profileAvatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatarPlaceholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--purple-gradient);
  color: white;
  font-size: 48px;
  font-weight: 600;
}

.avatarEditButton {
  position: absolute;
  bottom: var(--spacing-sm);
  right: var(--spacing-sm);
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  background-color: var(--primary-purple);
  color: white;
  border: 2px solid var(--card);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  opacity: 0;
  transition: all 0.15s ease-in-out;
  box-shadow: var(--shadow-md);
}

.avatarEditButton:hover {
  background-color: var(--accent-purple);
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.profileInfo {
  padding: 80px var(--spacing-2xl) var(--spacing-2xl);
}

.profileNameRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
}

.profileName {
  font-size: 32px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.25;
}

.nickname {
  font-weight: 400;
  color: var(--text-muted);
  margin-left: var(--spacing-sm);
  font-size: 20px;
}

.followButton {
  background-color: var(--primary-purple);
  color: white;
  border: 2px solid var(--primary-purple);
  border-radius: var(--radius-md);
  padding: 12px 24px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  transition: all 0.2s ease-in-out;
  min-height: 44px;
  font-size: 16px;
  position: relative;
  overflow: hidden;
  min-width: 120px;
  justify-content: center;
}

.followButton::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.followButton:hover::before {
  left: 100%;
}

.followButton:hover {
  background-color: var(--accent-purple);
  border-color: var(--accent-purple);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
}

.followButton:active {
  transform: translateY(0);
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.2);
}

.followButton.following {
  background-color: var(--card-hover);
  color: var(--primary-purple);
  border-color: var(--primary-purple);
}

.followButton.following:hover {
  background-color: var(--background-secondary);
  border-color: var(--accent-purple);
  color: var(--accent-purple);
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.2);
}

.followButton.pending {
  background-color: var(--warning-yellow);
  color: var(--text-primary);
  border-color: var(--warning-yellow);
  cursor: not-allowed;
}

.followButton.pending:hover {
  background-color: var(--warning-yellow);
  border-color: var(--warning-yellow);
  transform: none;
  box-shadow: none;
}

.followButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.buttonLoader {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-full);
  border-top-color: white;
  animation: spin 1s linear infinite;
}

.profileActions {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.editButton {
  background-color: var(--primary-purple);
  color: white;
  border: 1px solid var(--primary-purple);
  border-radius: var(--radius-md);
  padding: var(--spacing-md) var(--spacing-xl);
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  transition: all 0.15s ease-in-out;
  font-size: 14px;
  min-height: 40px;
}

.editButton:hover {
  background-color: var(--primary-purple-hover);
  border-color: var(--primary-purple-hover);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.privacyToggle {
  display: flex;
  align-items: center;
}

.toggleLabel {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
}

.toggleCheckbox {
  height: 0;
  width: 0;
  visibility: hidden;
  position: absolute;
}

.toggleSwitch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
  background-color: var(--border);
  border-radius: 24px;
  margin-left: var(--spacing-md);
  transition: 0.15s ease-in-out;
}

.toggleSwitch:after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background-color: var(--card);
  border-radius: var(--radius-full);
  transition: 0.15s ease-in-out;
}

.toggleCheckbox:checked + .toggleSwitch {
  background-color: var(--primary-purple);
}

.toggleCheckbox:checked + .toggleSwitch:after {
  left: calc(100% - 2px);
  transform: translateX(-100%);
}

.profileStats {
  display: flex;
  margin-bottom: var(--spacing-xl);
  gap: var(--spacing-xl);
}

.statItem {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.statValue {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.25;
}

.statLabel {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.33;
}

.profileBio {
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background-color: var(--background);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  line-height: 1.5;
}

.profileMeta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
}

.metaItem {
  display: flex;
  align-items: center;
  color: var(--text-secondary);
  font-size: 14px;
}

.metaItem i {
  margin-right: var(--spacing-sm);
  width: 16px;
}

@media (max-width: 768px) {
  .profileNameRow {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .profileActions {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }

  .editButton {
    justify-content: center;
  }
  
  .profileAvatar {
    left: 50%;
    transform: translateX(-50%);
  }
  
  .profileInfo {
    padding-top: 70px;
    text-align: center;
  }
  
  .profileStats {
    justify-content: center;
  }
  
  .profileMeta {
    justify-content: center;
  }
}