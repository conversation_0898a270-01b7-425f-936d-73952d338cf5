.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
  backdrop-filter: blur(3px);
  animation: fadeIn 0.3s ease-out;
  overflow-y: auto;
}

.modalContent {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  position: relative;
  z-index: 10000;
  margin: auto;
  animation: slideUp 0.3s ease-out;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 0;
}

.modalHeader h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.25rem;
  color: #6b7280;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s;
}

.closeButton:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.editForm {
  padding: 24px 24px 24px 24px;
}

.errorMessage {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 0.875rem;
}

.photoSection {
  margin-bottom: 24px;
}

.photoSection label {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.coverContainer {
  margin-bottom: 20px;
}

.coverPreview {
  width: 100%;
  height: 150px;
  background-color: #f3f4f6;
  background-size: cover;
  background-position: center;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.photoButton {
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  padding: 12px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.photoButton:hover {
  background-color: rgba(0, 0, 0, 0.8);
}

.avatarContainer {
  display: flex;
  justify-content: center;
}

.avatarPreview {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatarPreview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatarPlaceholder {
  font-size: 2rem;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
}

.avatarButton {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #3b82f6;
  color: white;
  border: 2px solid white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.avatarButton:hover {
  background-color: #2563eb;
}

.formGroup {
  margin-bottom: 20px;
}

.formGroup label {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 6px;
}

.formGroup input,
.formGroup textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.formGroup input:focus,
.formGroup textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.formGroup textarea {
  resize: vertical;
  min-height: 100px;
}

.modalActions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.cancelButton {
  padding: 12px 24px;
  border: 1px solid #d1d5db;
  background-color: white;
  color: #374151;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.cancelButton:hover:not(:disabled) {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.saveButton {
  padding: 12px 24px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.saveButton:hover:not(:disabled) {
  background-color: #2563eb;
}

.saveButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive design */
@media (max-width: 640px) {
  .modalOverlay {
    padding: 10px;
  }
  
  .modalContent {
    max-height: 95vh;
  }
  
  .modalHeader {
    padding: 16px 16px 0 16px;
  }
  
  .editForm {
    padding: 0 16px 16px 16px;
  }
  
  .coverPreview {
    height: 120px;
  }
  
  .avatarPreview {
    width: 100px;
    height: 100px;
  }
  
  .modalActions {
    flex-direction: column;
  }
  
  .cancelButton,
  .saveButton {
    width: 100%;
    justify-content: center;
  }
}
