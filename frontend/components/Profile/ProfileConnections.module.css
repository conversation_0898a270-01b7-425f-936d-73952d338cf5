.connectionsContainer {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-top: 20px;
}

.connectionCard {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: var(--white);
  border-radius: 8px;
  box-shadow: var(--shadow);
  transition: transform 0.2s, box-shadow 0.2s;
  text-decoration: none;
  color: inherit;
}

.connectionCard:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-hover);
}

.connectionAvatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 16px;
  flex-shrink: 0;
}

.connectionAvatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatarPlaceholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--secondary-navy);
  color: var(--white);
  font-size: 20px;
  font-weight: bold;
}

.connectionInfo {
  flex: 1;
  overflow: hidden;
}

.connectionName {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
  color: var(--text-dark);
}

.nickname {
  font-weight: normal;
  color: var(--text-light);
  margin-left: 4px;
  font-size: 14px;
}

.connectionBio {
  font-size: 14px;
  color: var(--text-light);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.loadingContainer, .errorContainer, .emptyContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loadingSpinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid var(--primary-navy);
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.emptyContainer i {
  font-size: 48px;
  color: var(--text-light);
  margin-bottom: 16px;
}

.emptyContainer h3 {
  margin-bottom: 8px;
  color: var(--text-dark);
}

.emptyContainer p {
  color: var(--text-light);
}

@media (max-width: 768px) {
  .connectionsContainer {
    grid-template-columns: 1fr;
  }
}