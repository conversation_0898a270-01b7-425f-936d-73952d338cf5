.backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 999;
}

.notificationPanel {
  position: fixed;
  top: 60px;
  right: 20px;
  width: 380px;
  max-width: calc(100vw - 40px);
  background-color: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  z-index: 1000;
  max-height: 500px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panelHeader {
  padding: var(--spacing-xl);
  border-bottom: 1px solid var(--border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--card);
}

.panelHeader h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.headerActions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.markAllButton {
  background: none;
  border: none;
  color: var(--primary-purple);
  font-size: 12px;
  cursor: pointer;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: background-color 0.15s ease;
}

.markAllButton:hover {
  background-color: var(--card-hover);
}

.closeButton {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 14px;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: color 0.15s ease;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeButton:hover {
  color: var(--text-primary);
  background-color: var(--card-hover);
}

.panelContent {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.notificationList {
  /* No additional styles needed - items handle their own styling */
}

.loadingState,
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--text-muted);
  gap: 10px;
}

.loadingState i,
.emptyState i {
  font-size: 24px;
  margin-bottom: 5px;
}

.loadingState span,
.emptyState span {
  font-size: 14px;
}

/* Responsive design */
@media (max-width: 768px) {
  .notificationPanel {
    top: 50px;
    right: 10px;
    left: 10px;
    width: auto;
    max-width: none;
    max-height: 70vh;
  }
  
  .panelHeader {
    padding: 15px;
  }
  
  .panelHeader h3 {
    font-size: 16px;
  }
  
  .markAllButton {
    font-size: 11px;
  }
}

/* Custom scrollbar */
.panelContent::-webkit-scrollbar {
  width: 6px;
}

.panelContent::-webkit-scrollbar-track {
  background: var(--background);
}

.panelContent::-webkit-scrollbar-thumb {
  background: var(--border);
  border-radius: 3px;
}

.panelContent::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}
