.notificationItem {
  display: flex;
  align-items: flex-start;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border);
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
}

.notificationItem:hover {
  background-color: var(--muted);
}

.notificationItem:last-child {
  border-bottom: none;
}

.notificationItem.unread {
  background-color: var(--primary-light);
}

.notificationItem.unread:hover {
  background-color: var(--primary-lighter);
}

.groupAvatar {
  position: relative;
  margin-right: var(--spacing-md);
  flex-shrink: 0;
}

.avatarImage {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-full);
  object-fit: cover;
  border: 2px solid var(--border);
}

.avatarPlaceholder {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-full);
  background-color: var(--primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  border: 2px solid var(--border);
}

.unreadBadge {
  position: absolute;
  top: -4px;
  right: -4px;
  background-color: var(--danger);
  color: white;
  font-size: 11px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: var(--radius-full);
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--card);
}

.notificationContent {
  flex: 1;
  min-width: 0;
}

.notificationHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xs);
}

.groupTitle {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 15px;
  line-height: 1.3;
  flex: 1;
  margin-right: var(--spacing-sm);
}

.notificationTime {
  color: var(--text-secondary);
  font-size: 12px;
  white-space: nowrap;
  flex-shrink: 0;
}

.lastMessage {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-xs);
  line-height: 1.4;
}

.senderName {
  color: var(--primary);
  font-weight: 500;
  font-size: 13px;
  flex-shrink: 0;
}

.messagePreview {
  color: var(--text-secondary);
  font-size: 13px;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.loadingState {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md);
  color: var(--text-secondary);
  gap: var(--spacing-sm);
}

.loadingState i {
  font-size: 14px;
}

.loadingState span {
  font-size: 13px;
}

/* Responsive design */
@media (max-width: 768px) {
  .notificationItem {
    padding: var(--spacing-sm);
  }
  
  .groupAvatar {
    margin-right: var(--spacing-sm);
  }
  
  .avatarImage,
  .avatarPlaceholder {
    width: 40px;
    height: 40px;
  }
  
  .avatarPlaceholder {
    font-size: 16px;
  }
  
  .unreadBadge {
    font-size: 10px;
    padding: 1px 4px;
    min-width: 16px;
    height: 16px;
  }
  
  .groupTitle {
    font-size: 14px;
  }
  
  .notificationTime {
    font-size: 11px;
  }
  
  .senderName,
  .messagePreview {
    font-size: 12px;
  }
}
