.backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 999;
}

.groupNotificationPanel {
  position: fixed;
  top: 60px;
  right: 20px;
  width: 400px;
  max-width: calc(100vw - 40px);
  background-color: var(--card);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  z-index: 1000;
  max-height: 500px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panelHeader {
  padding: var(--spacing-xl);
  border-bottom: 1px solid var(--border);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--card);
}

.headerTitle {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.headerTitle i {
  color: var(--primary);
  font-size: 18px;
}

.headerTitle h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.headerActions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.markAllButton {
  background: none;
  border: none;
  color: var(--primary);
  font-size: 14px;
  cursor: pointer;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius);
  transition: background-color 0.2s ease;
}

.markAllButton:hover {
  background-color: var(--primary-light);
}

.closeButton {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 16px;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius);
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.closeButton:hover {
  background-color: var(--muted);
  color: var(--text-primary);
}

.panelContent {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);
  text-align: center;
  min-height: 200px;
}

.emptyState i {
  font-size: 48px;
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

.emptyState span {
  font-size: 16px;
}

.notificationList {
  display: flex;
  flex-direction: column;
}

/* Responsive design */
@media (max-width: 768px) {
  .groupNotificationPanel {
    right: 10px;
    left: 10px;
    width: auto;
    max-width: none;
  }
  
  .panelHeader {
    padding: var(--spacing-md);
  }
  
  .headerTitle h3 {
    font-size: 16px;
  }
  
  .markAllButton {
    font-size: 12px;
    padding: var(--spacing-xs);
  }
}
