.widget {
  background-color: var(--card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border);
  overflow: hidden;
  transition: all 0.15s ease-in-out;
}

.widget:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.widgetHeader {
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid var(--border);
  font-size: 16px;
  font-weight: 600;
  background: var(--purple-gradient-light);
  color: var(--primary-purple);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.seeAllLink {
  color: var(--primary-purple);
  text-decoration: none;
  font-size: 13px;
  font-weight: 500;
  transition: color 0.2s ease;
}

.seeAllLink:hover {
  color: var(--primary-purple-hover);
}

.widgetContent {
  padding: var(--spacing-lg) var(--spacing-xl);
}

.loadingState,
.errorState,
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xl) var(--spacing-lg);
  text-align: center;
  color: var(--text-secondary);
  font-size: 14px;
}

.loadingState i {
  color: var(--primary-purple);
  font-size: 18px;
}

.errorState i {
  color: var(--error);
  font-size: 18px;
}

.emptyState i {
  color: var(--text-muted);
  font-size: 24px;
  margin-bottom: var(--spacing-sm);
}

.browseLink {
  color: var(--primary-purple);
  text-decoration: none;
  font-weight: 500;
  font-size: 13px;
  margin-top: var(--spacing-sm);
  transition: color 0.2s ease;
}

.browseLink:hover {
  color: var(--primary-purple-hover);
}

.recommendationsList {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.groupCard {
  margin: 0;
  box-shadow: none;
  border-radius: var(--radius-lg);
  overflow: hidden;
  border: 1px solid var(--border);
  transition: all 0.15s ease-in-out;
  background: var(--white);
}

.groupCard:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
  border-color: var(--primary-purple);
}

.groupLink {
  text-decoration: none;
  color: inherit;
  display: block;
}

.groupHeader {
  height: 60px;
  background: var(--purple-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.groupAvatar {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.groupAvatarPlaceholder {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.groupInfo {
  padding: var(--spacing-md);
  text-align: left;
}

.groupName {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 6px;
  font-size: 15px;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.groupMeta {
  display: flex;
  flex-direction: column;
  gap: 3px;
  margin-bottom: var(--spacing-sm);
}

.memberCount {
  color: var(--text-secondary);
  font-size: 12px;
  font-weight: 500;
}

.followedMembers {
  color: var(--primary-purple);
  font-size: 11px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
}

.followedMembers::before {
  content: "👥";
  font-size: 10px;
}

.groupDescription {
  color: var(--text-secondary);
  font-size: 12px;
  line-height: 1.4;
  margin-top: var(--spacing-xs);
}

.groupActions {
  padding: 0 var(--spacing-md) var(--spacing-md);
  display: flex;
  justify-content: center;
}

.joinButton {
  background: var(--primary-purple);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  min-width: 90px;
  justify-content: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.joinButton:hover:not(:disabled) {
  background: var(--primary-purple-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.joinButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.joinButton i {
  font-size: 11px;
}

/* Responsive design */
@media (max-width: 768px) {
  .widgetHeader {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 15px;
  }

  .widgetContent {
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .recommendationsList {
    gap: var(--spacing-md);
  }

  .groupInfo {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .groupName {
    font-size: 14px;
  }

  .groupActions {
    padding: 0 var(--spacing-md) var(--spacing-sm);
  }

  .joinButton {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: 12px;
    min-width: 80px;
  }
}