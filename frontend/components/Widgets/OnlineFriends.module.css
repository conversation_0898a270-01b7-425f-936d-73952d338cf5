.widget {
  background-color: var(--card);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border);
  overflow: hidden;
  transition: all 0.15s ease-in-out;
}

.widget:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.widgetHeader {
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid var(--border);
  font-size: 16px;
  font-weight: 600;
  background: var(--purple-gradient-light);
  color: var(--primary-purple);
}

.widgetContent {
  padding: var(--spacing-lg) var(--spacing-xl);
}

.onlineFriends {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.friendItem {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-sm);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all 0.15s ease-in-out;
}

.friendItem:hover {
  background-color: var(--card-hover);
  transform: translateX(4px);
  box-shadow: var(--shadow-sm);
}

.friendName {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.loadingState,
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px 10px;
  text-align: center;
  color: var(--text-light);
}

.loadingState i,
.emptyState i {
  font-size: 24px;
  color: var(--border-gray);
}

.loadingState span,
.emptyState span {
  font-size: 12px;
}

@media (max-width: 768px) {
  .widget {
    margin: 0 0 16px 0;
  }
  
  .widgetHeader {
    padding: 12px 16px;
  }
  
  .widgetContent {
    padding: 12px 16px;
  }
  
  .friendName {
    font-size: 12px;
  }
  
  .loadingState,  
  .emptyState {
    padding: 16px 12px;
  }
  
  .loadingState i,
  .emptyState i {
    font-size: 20px;
  }
  
  .loadingState span,
  .emptyState span {
    font-size: 10px;
  }
} 
