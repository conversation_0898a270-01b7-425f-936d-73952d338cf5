.registerForm {
  padding: 0;
  max-width: 100%;
  margin: 0;
}

/* Progress Bar */
.progressContainer {
  margin-bottom: var(--spacing-2xl);
}

.progressBar {
  width: 100%;
  height: 8px;
  background-color: var(--border-light);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: var(--spacing-sm);
}

.progressFill {
  height: 100%;
  background: var(--purple-gradient);
  border-radius: var(--radius-full);
  transition: width 0.3s ease-in-out;
}

.progressText {
  text-align: center;
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Step Content */
.stepContent {
  margin-bottom: var(--spacing-2xl);
}

/* Section Titles */
.sectionTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: var(--spacing-xl) 0 var(--spacing-lg) 0;
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--border-light);
}

.sectionTitle:first-of-type {
  margin-top: 0;
}

/* Submit Section */
.submitSection {
  margin-top: var(--spacing-2xl);
  text-align: center;
}

.stepTitle {
  font-size: 1.875rem;
  font-weight: 700;
  margin-bottom: var(--spacing-sm);
  background: var(--purple-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
}

.stepDescription {
  text-align: center;
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-2xl);
  line-height: 1.5;
}

/* Step Navigation */
.stepNavigation {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
}

.stepNavigation button {
  flex: 1;
}

.btnPrimary {
  background: var(--purple-gradient);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  font-size: 16px;
  font-weight: 600;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  min-height: 52px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.btnPrimary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.4);
}

.btnSecondary {
  background-color: var(--card-hover);
  color: var(--text-primary);
  border: 2px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  font-size: 16px;
  font-weight: 600;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  min-height: 52px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btnSecondary:hover {
  background-color: var(--border-light);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.formGroup {
  margin-bottom: var(--spacing-xl);
}

.formLabel {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
  font-size: 14px;
  color: var(--text-primary);
  line-height: 1.25;
}

.required {
  color: var(--error);
}

.optional {
  color: var(--text-muted);
  font-weight: 400;
  font-size: 14px;
}

.formInput, .formTextarea {
  width: 100%;
  padding: var(--spacing-lg);
  border: 2px solid var(--border);
  border-radius: var(--radius-lg);
  font-size: 16px;
  font-family: inherit;
  background: var(--card);
  color: var(--text-primary);
  transition: all 0.15s ease-in-out;
}

.formInput:focus, .formTextarea:focus {
  border-color: var(--primary-purple);
  outline: none;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  transform: translateY(-1px);
}

.formInput::placeholder, .formTextarea::placeholder {
  color: var(--text-muted);
}

.inputError {
  border-color: var(--error);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.errorText {
  color: var(--error);
  font-size: 14px;
  margin-top: var(--spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.errorText::before {
  content: '⚠️';
  font-size: 12px;
}

.errorMessage {
  background: var(--error-light);
  border: 1px solid var(--error);
  color: var(--error);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-lg);
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.errorMessage::before {
  content: '⚠️';
}

.errorMessage.conflict {
  background: var(--warning-light);
  border-color: var(--warning);
  color: var(--warning);
}

.errorMessage.conflict::before {
  content: '💡';
}

.errorActions {
  margin-top: var(--spacing-md);
  display: flex;
  gap: var(--spacing-md);
}

.errorActions .btn-outline {
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: 14px;
  min-height: 36px;
}

.nameFields {
  display: flex;
  gap: 16px;
}

.nameFields .formGroup {
  flex: 1;
}

.passwordStrength {
  margin-top: 8px;
  display: flex;
  align-items: center;
}

.strengthBar {
  flex: 1;
  height: 6px;
  background-color: var(--border-gray);
  border-radius: 3px;
  overflow: hidden;
  margin-right: 10px;
}

.strengthFill {
  height: 100%;
  transition: width 0.3s, background-color 0.3s;
}

.strengthLabel {
  font-size: 14px;
  color: var(--text-light);
  min-width: 70px;
}

.passwordHint {
  font-size: 12px;
  color: var(--text-light);
  margin-top: 4px;
}

.avatarUpload {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-md);
}

.avatarPreview {
  width: 120px;
  height: 120px;
  border-radius: var(--radius-full);
  overflow: hidden;
  background: var(--purple-gradient-light);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 3px dashed var(--primary-purple);
  transition: all 0.15s ease-in-out;
  position: relative;
}

.avatarPreview:hover {
  transform: scale(1.05);
  border-color: var(--accent-purple);
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.2);
}

.avatarImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatarPlaceholder {
  font-size: 14px;
  color: var(--primary-purple);
  text-align: center;
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: 500;
}

.uploadIcon {
  font-size: 2rem;
  margin-bottom: var(--spacing-xs);
}

.fileInput {
  display: none;
}

.charCounter {
  font-size: 12px;
  color: var(--text-muted);
  text-align: right;
  margin-top: var(--spacing-xs);
}

.btnRegister {
  background: var(--purple-gradient);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  font-size: 18px;
  font-weight: 600;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  min-height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
  gap: var(--spacing-sm);
}

.btnRegister:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(99, 102, 241, 0.4);
}

.btnRegister:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.btnRegister:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.registerFooter {
  text-align: center;
  padding-top: var(--spacing-xl);
  border-top: 1px solid var(--border);
  color: var(--text-muted);
  font-size: 14px;
}

.registerLink {
  color: var(--primary-purple);
  text-decoration: none;
  font-weight: 600;
  transition: color 0.15s ease-in-out;
}

.registerLink:hover {
  color: var(--primary-purple-hover);
  text-decoration: underline;
}

@media (max-width: 768px) {
  .nameFields {
    flex-direction: column;
    gap: 0;
  }
  
  .registerForm {
    padding: 20px 15px;
  }
}