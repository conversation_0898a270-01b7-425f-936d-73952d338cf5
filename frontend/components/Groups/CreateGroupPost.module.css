.createPost {
  padding: 1rem;
}

.createPostHeader {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.postInput {
  flex: 1;
  min-height: 80px;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-family: inherit;
  font-size: 0.95rem;
  line-height: 1.5;
  resize: vertical;
  background: var(--background-color);
  color: var(--text-primary);
  transition: border-color 0.2s ease;
}

.postInput:focus {
  outline: none;
  border-color: var(--primary-navy);
}

.postInput:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.postInput::placeholder {
  color: var(--text-muted);
}

.imagePreview {
  position: relative;
  margin-bottom: 1rem;
  border-radius: 8px;
  overflow: hidden;
  background: var(--background-color);
  border: 1px solid var(--border-color);
}

.imagePreview img {
  width: 100%;
  height: auto;
  display: block;
  max-width: 100%;
  object-fit: contain;
  background: var(--white);
}

.imageInfo {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: white;
  padding: 1rem 0.75rem 0.75rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.imageName {
  font-size: 0.9rem;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.imageDetails {
  font-size: 0.8rem;
  opacity: 0.9;
}

.removeImage {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.removeImage:hover {
  background: rgba(0, 0, 0, 0.9);
}

.errorMessage {
  background: var(--error-background);
  color: var(--error-color);
  padding: 0.75rem;
  border-radius: 6px;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  border: 1px solid var(--error-border);
}

.postActions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.postOptions {
  display: flex;
  gap: 1rem;
}

.postOption {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  color: var(--text-secondary);
  transition: all 0.2s ease;
  border: none;
  background: transparent;
}

.postOption:hover {
  background: var(--hover-background);
  color: var(--primary-navy);
}

.postOption i {
  font-size: 1rem;
}

.postButton {
  padding: 0.5rem 1.5rem;
  font-size: 0.9rem;
  min-width: 80px;
}

.postButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.postButton i {
  margin-right: 0.5rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .createPost {
    padding: 0.75rem;
  }

  .createPostHeader {
    gap: 0.75rem;
  }

  .postInput {
    min-height: 60px;
    padding: 0.5rem;
    font-size: 0.9rem;
  }

  .imagePreview {
    margin-bottom: 0.75rem;
  }

  .imagePreview img {
    max-height: 50vh;
  }

  .postActions {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .postOptions {
    justify-content: center;
  }

  .postButton {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .imagePreview img {
    max-height: 40vh;
  }
}
