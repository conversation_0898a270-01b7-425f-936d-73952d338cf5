.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modalContent {
  background: var(--card-background);
  border-radius: 12px;
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0 24px;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 24px;
}

.modalHeader h2 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.closeButton:hover {
  background-color: var(--hover-background);
  color: var(--text-primary);
}

.closeButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.form {
  padding: 0 24px 24px 24px;
}

.formGroup {
  margin-bottom: 20px;
}

.formGroup label {
  display: block;
  margin-bottom: 8px;
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.9rem;
}

.formGroup input,
.formGroup textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--input-background);
  color: var(--text-primary);
  font-size: 1rem;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.formGroup input:focus,
.formGroup textarea:focus {
  outline: none;
  border-color: var(--primary-navy);
}

.formGroup textarea {
  resize: vertical;
  min-height: 100px;
}

.inputError {
  border-color: var(--error-color) !important;
}

.errorMessage {
  display: block;
  color: #dc2626;
  font-size: 0.85rem;
  margin-top: 4px;
  font-weight: 500;
}

.generalError {
  background-color: rgba(239, 68, 68, 0.15);
  border: 1px solid rgba(239, 68, 68, 0.3);
  color: #dc2626;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 0.9rem;
}

.imageSection {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.imageGroup {
  display: flex;
  flex-direction: column;
}

.imageGroup label {
  margin-bottom: 8px;
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.9rem;
}

.coverUpload {
  width: 100%;
  height: 120px;
  border: 2px dashed var(--border-color);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  overflow: hidden;
}

.coverUpload:hover {
  border-color: var(--primary-navy);
  background-color: var(--hover-background);
}

.uploadPlaceholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
  text-align: center;
}

.uploadPlaceholder i {
  font-size: 2rem;
}

.uploadPlaceholder span {
  font-size: 0.9rem;
}

.avatarUpload {
  width: 120px;
  height: 120px;
  border: 2px dashed var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  align-self: center;
}

.avatarUpload:hover {
  border-color: var(--primary-navy);
  background-color: var(--hover-background);
}

.avatarPlaceholder {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
}

.avatarPlaceholder i {
  font-size: 2rem;
}

.avatarPreviewImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.imageOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.coverUpload:hover .imageOverlay,
.avatarUpload:hover .avatarOverlay {
  opacity: 1;
}

.avatarOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
  border-radius: 50%;
  gap: 8px;
}

.avatarOverlay i {
  color: white;
  font-size: 1.2rem;
}

.removeImageButton {
  background: #dc2626;
  border: none;
  color: white;
  padding: 8px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.removeImageButton:hover {
  background: #b91c1c;
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.removeImageButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.formActions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

.formActions button {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-size: 1rem;
}

.formActions button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive design */
@media (max-width: 768px) {
  .modalOverlay {
    padding: 10px;
  }
  
  .modalContent {
    max-height: 95vh;
  }
  
  .modalHeader {
    padding: 20px 20px 0 20px;
  }
  
  .form {
    padding: 0 20px 20px 20px;
  }
  
  .imageSection {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .avatarUpload {
    width: 100px;
    height: 100px;
  }
  
  .coverUpload {
    height: 100px;
  }
  
  .formActions {
    flex-direction: column-reverse;
  }
  
  .formActions button {
    width: 100%;
  }
}
