/* Group Card */
.groupCard {
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid #e5e7eb;
}

.groupCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Group Header */
.groupHeader {
  height: 120px;
  background: linear-gradient(135deg, var(--primary-navy), var(--secondary-navy));
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.groupIcon {
  width: 60px;
  height: 60px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  font-weight: 600;
  color: white;
  border: 3px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

.groupAvatarImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

/* Status Badge */
.statusBadge {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.statusBadge.creator {
  background-color: rgba(251, 191, 36, 0.9);
  color: #92400e;
}

.statusBadge.member {
  background-color: rgba(34, 197, 94, 0.9);
  color: #166534;
}

.statusBadge.pending {
  background-color: rgba(59, 130, 246, 0.9);
  color: #1e40af;
}

/* Group Info */
.groupInfo {
  padding: 20px;
}

.groupName {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--primary-navy);
  margin-bottom: 8px;
  line-height: 1.3;
}

.groupDescription {
  font-size: 0.875rem;
  color: var(--text-light);
  margin-bottom: 16px;
  line-height: 1.4;
}

.groupMeta {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 0.75rem;
  color: var(--text-light);
}

.memberCount,
.creatorInfo {
  display: flex;
  align-items: center;
  gap: 6px;
}

.memberCount i {
  color: #3b82f6;
}

.creatorInfo i {
  color: #6b7280;
}

/* Error Message */
.errorMessage {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.75rem;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* Group Actions */
.groupActions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.groupActions button {
  font-size: 0.8125rem;
  padding: 8px 12px;
  flex: 1;
  min-width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  white-space: nowrap;
}

.groupActions button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.groupActions button i {
  font-size: 0.75rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .groupHeader {
    height: 100px;
  }
  
  .groupIcon {
    width: 50px;
    height: 50px;
    font-size: 24px;
  }
  
  .groupInfo {
    padding: 16px;
  }
  
  .groupName {
    font-size: 1rem;
  }
  
  .groupDescription {
    font-size: 0.8125rem;
  }
  
  .statusBadge {
    top: 8px;
    right: 8px;
    font-size: 0.6875rem;
    padding: 3px 6px;
  }
}

@media (max-width: 480px) {
  .groupActions {
    flex-direction: column;
  }
  
  .groupActions button {
    flex: none;
    width: 100%;
    min-width: auto;
  }
  
  .groupMeta {
    font-size: 0.6875rem;
  }
  
  .errorMessage {
    font-size: 0.6875rem;
    padding: 6px 10px;
  }
}
