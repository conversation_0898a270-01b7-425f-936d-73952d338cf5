.groupChat {
  display: flex;
  flex-direction: column;
  height: 85vh;
  background: var(--white);
  border-radius: 8px;
  box-shadow: var(--shadow);
  padding: 0;
}

.messagesContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  background: var(--background-light);
  padding: 0 0 8px 0;
  height: 65vh;
  /* Add a visible scrollbar for chat messages */
  scrollbar-width: thin;
  scrollbar-color: var(--primary-navy) var(--background-light);
}

.messagesContainer::-webkit-scrollbar {
  width: 8px;
}
.messagesContainer::-webkit-scrollbar-thumb {
  background: var(--primary-navy);
  border-radius: 4px;
}
.messagesContainer::-webkit-scrollbar-track {
  background: var(--background-light);
}

.messagesList {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: var(--background-light);
  /* Add a visible scrollbar for the messages list */
  scrollbar-width: thin;
  scrollbar-color: var(--primary-navy) var(--background-light);
}

.messagesList::-webkit-scrollbar {
  width: 8px;
}
.messagesList::-webkit-scrollbar-thumb {
  background: var(--primary-navy);
  border-radius: 4px;
}
.messagesList::-webkit-scrollbar-track {
  background: var(--background-light);
}

.message {
  margin-bottom: 6px;
  padding: 7px 12px;
  background: var(--card-background);
  border-radius: 16px 16px 16px 4px;
  min-width: 10%;
  max-width: 50%;
  font-size: 14px;
  line-height: 1.4;
  align-self: flex-start;
  color: var(--text-dark);
  /* Left side for others */
  margin-right: auto;
  margin-left: 0;
  box-shadow: 0 1px 2px rgba(0,0,0,0.04);
  word-break: break-word;
  /* position: relative;
  display: inline-block; */
}

.ownMessage {
  margin-bottom: 6px;
  padding: 7px 12px;
  background: var(--success);
  color: var(--white);
  border-radius: 16px 16px 4px 16px;
  min-width: 10%;
  max-width: 50%;
  font-size: 14px;
  line-height: 1.4;
  align-self: flex-end;
  /* Right side for self */
  margin-left: auto;
  margin-right: 0;
  box-shadow: 0 1px 2px rgba(0,0,0,0.04);
  word-break: break-word;
  /* position: relative;
  display: inline-block; */
}

.senderName {
  display: block;
  font-weight: 600;
  font-size: 12px;
  margin-bottom: 2px;
  color: #3b5998;
  background: #d9dee7;
  padding: 2px 8px 2px 8px;
  border-radius: var(--spacing-sm);
  width: fit-content;
  margin-left: -8px;
  margin-top: -7px;
  margin-bottom: 4px;
}

.messageContainer {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  position: relative;
  justify-content: space-between;
}

.messageContent {
  padding-right: 10px;
}

.text {
  margin-right: 5px;
}

.timestamp {
  font-size: 0.8em;
  color: var(--text-light);
}

.inputForm {
  display: flex;
  border-top: 1px solid var(--border-gray);
  padding: 12px;
  background: var(--white);
}

.input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--border-gray);
  border-radius: 4px;
  margin-right: 8px;
  background: var(--white);
  color: var(--primary-navy);
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  padding: 32px 0;
}

.chatHeader {
  padding: 12px 20px;
  border-bottom: 1px solid var(--border-gray);
  background: var(--white);
  font-weight: 500;
  color: var(--primary-navy);
  display: flex;
  align-items: center;
  gap: 8px;
}

.messageForm {
  border-top: 1px solid var(--border-gray);
  background: var(--white);
  padding: 12px 16px;
}

.messageInputContainer {
  display: flex;
  align-items: center;
  gap: 8px;
}

.messageInput {
  flex: 1;
  padding: 10px 14px;
  border: 1px solid var(--border-gray);
  border-radius: 6px;
  font-size: 15px;
  background: var(--white);
  color: var(--primary-navy);
}

.emojiButton {
  background: var(--card);
  border: 1px solid var(--border-gray);
  border-radius: 6px;
  padding: 10px 12px;
  font-size: 16px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
}

.emojiButton:hover {
  background: var(--card-hover);
  color: var(--primary-purple);
  border-color: var(--primary-purple);
  transform: translateY(-1px);
}

.emojiButton:active {
  transform: translateY(0);
}

.sendButton {
  background: var(--primary-navy);
  color: var(--white);
  border: none;
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
}

.sendButton:hover:not(:disabled) {
  background: var(--primary-purple-hover);
  transform: translateY(-1px);
}

.sendButton:active:not(:disabled) {
  transform: translateY(0);
}

.sendButton:disabled {
  background: var(--border-gray);
  color: var(--text-light);
  cursor: not-allowed;
}

.typingIndicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 8px 0 0 8px;
}

.typingDots {
  display: flex;
  align-items: center;
  gap: 2px;
}
.typingDots span {
  display: inline-block;
  width: 6px;
  height: 6px;
  background: var(--text-light);
  border-radius: 50%;
  animation: typingBlink 1s infinite alternate;
}
.typingDots span:nth-child(2) {
  animation-delay: 0.2s;
}
.typingDots span:nth-child(3) {
  animation-delay: 0.4s;
}
@keyframes typingBlink {
  0% { opacity: 0.3; }
  100% { opacity: 1; }
}

.typingText {
  font-size: 0.95em;
  color: var(--text-light);
}

.loadingContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: var(--primary-navy);
  padding: 32px 0;
}

.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--error-red);
  padding: 32px 0;
  gap: 10px;
}

/* Responsive design */
@media (max-width: 768px) {
  .messageInputContainer {
    gap: 6px;
  }

  .messageInput {
    font-size: 14px;
    padding: 8px 12px;
  }

  .emojiButton,
  .sendButton {
    min-width: 36px;
    height: 36px;
    padding: 8px 10px;
    font-size: 14px;
  }

  .messageForm {
    padding: 10px 12px;
  }
}
