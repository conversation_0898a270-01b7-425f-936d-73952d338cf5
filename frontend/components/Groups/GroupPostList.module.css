.postList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem 0;
}

.loadingContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 2rem;
  color: var(--text-secondary);
}

.loadingContainer i {
  font-size: 1.2rem;
  color: var(--primary-navy);
}

.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  text-align: center;
  color: var(--text-secondary);
}

.errorContainer i {
  font-size: 2rem;
  color: var(--error-color);
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 3rem 2rem;
  text-align: center;
  color: var(--text-secondary);
}

.emptyState i {
  font-size: 3rem;
  color: var(--text-muted);
  margin-bottom: 0.5rem;
}

.emptyState h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.25rem;
  font-weight: 600;
}

.emptyState p {
  margin: 0;
  font-size: 0.95rem;
  line-height: 1.5;
  max-width: 400px;
}

.postsContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .postList {
    padding: 0.5rem 0;
    gap: 0.75rem;
  }
  
  .emptyState {
    padding: 2rem 1rem;
  }
  
  .emptyState i {
    font-size: 2.5rem;
  }
  
  .emptyState h3 {
    font-size: 1.1rem;
  }
  
  .emptyState p {
    font-size: 0.9rem;
  }
}
