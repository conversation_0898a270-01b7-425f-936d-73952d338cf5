.post {
  padding: 1rem;
}

.postHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.postUser {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.userAvatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.userAvatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatarPlaceholder {
  width: 100%;
  height: 100%;
  background: var(--primary-navy);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
}

.postUserInfo h4 {
  margin: 0;
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--text-primary);
}

.postMeta {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.postMeta i {
  font-size: 0.75rem;
}

.postMenu {
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  position: relative;
}

.postMenu:hover {
  background: var(--hover-background);
}

.menuButton {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-secondary);
  padding: 0.5rem;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.menuButton:hover {
  background-color: var(--hover-background);
}

.menuButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.dropdownMenu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  z-index: 10;
  min-width: 120px;
}

.dropdownMenu button {
  border: none;
  background: none;
  text-align: left;
  padding: 0.5rem 1rem;
  cursor: pointer;
  border-radius: 4px;
  width: 100%;
  font-size: 0.9rem;
  color: var(--text-primary);
  transition: background-color 0.2s ease;
}

.dropdownMenu button:hover {
  background-color: var(--hover-background);
}

.dropdownMenu button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.editPost {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.editTextArea {
  width: 100%;
  min-height: 80px;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-family: inherit;
  font-size: 0.95rem;
  line-height: 1.5;
  resize: vertical;
  background: var(--background-color);
  color: var(--text-primary);
  transition: border-color 0.2s ease;
}

.editTextArea:focus {
  outline: none;
  border-color: var(--primary-navy);
}

.editTextArea:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.editActions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.editActions button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.editActions button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.cancelButton {
  background-color: var(--border-color);
  color: var(--text-primary);
}

.cancelButton:hover:not(:disabled) {
  background-color: var(--text-secondary);
  color: white;
}

.saveButton {
  background-color: var(--primary-navy);
  color: white;
}

.saveButton:hover:not(:disabled) {
  background-color: var(--secondary-navy);
}

.postContent {
  margin-bottom: 1rem;
  line-height: 1.6;
  color: var(--text-primary);
  white-space: pre-wrap;
  word-wrap: break-word;
}

.postImage {
  margin-bottom: 1rem;
  border-radius: 8px;
  overflow: hidden;
  background: var(--background);
  border: 1px solid var(--border);
  cursor: pointer;
  transition: all 0.2s ease;
  max-width: 100%;
  max-height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.postImage:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.postImage img {
  width: auto;
  height: auto;
  display: block;
  max-width: 100%;
  max-height: 500px;
  object-fit: contain;
  background: var(--white);
  transition: transform 0.2s ease;
  border-radius: 6px;
}

/* Ensure images don't get too small on very wide screens */
@media (min-width: 1200px) {
  .postImage img {
    min-width: 300px;
  }
}

.postImage:hover img {
  transform: scale(1.02);
}

.postStats {
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 0.5rem;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.postActionsRow {
  display: flex;
  gap: 1rem;
}

.postAction {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  color: var(--text-secondary);
  transition: all 0.2s ease;
  flex: 1;
  justify-content: center;
  border: none;
  background: transparent;
}

.postAction:hover {
  background: var(--hover-background);
  color: var(--primary-navy);
}

.postAction i {
  font-size: 1rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .post {
    padding: 0.75rem;
  }
  
  .postUser {
    gap: 0.5rem;
  }
  
  .userAvatar {
    width: 36px;
    height: 36px;
  }
  
  .postUserInfo h4 {
    font-size: 0.9rem;
  }
  
  .postMeta {
    font-size: 0.75rem;
  }
  
  .postContent {
    font-size: 0.95rem;
  }

  .postImage {
    margin-bottom: 0.75rem;
    max-height: 400px;
  }

  .postImage img {
    max-height: 400px;
    max-width: 100%;
  }

  .postActionsRow {
    gap: 0.5rem;
  }

  .postAction {
    padding: 0.5rem;
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .postImage {
    max-height: 300px;
  }

  .postImage img {
    max-height: 300px;
    max-width: 100%;
  }
}

@media (max-width: 320px) {
  .postImage {
    max-height: 250px;
  }

  .postImage img {
    max-height: 250px;
  }
}
