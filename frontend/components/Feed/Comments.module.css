.commentsOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.commentsContainer {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.commentsHeader {
  padding: 16px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.commentsHeader h3 {
  margin: 0;
  font-size: 1.2rem;
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #666;
}

.closeButton:hover {
  color: #333;
}

.commentsList {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.comment {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}

.comment:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.commentHeader {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.commentDetails {
  margin-left: 12px;
}

.avatarLink {
  text-decoration: none;
  transition: opacity 0.2s;
}

.avatarLink:hover {
  opacity: 0.8;
}

.userNameLink {
  text-decoration: none;
  color: inherit;
}

.userNameLink:hover .userName {
  color: var(--primary-purple, #8b5cf6);
}

.userName {
  font-weight: 600;
  margin-right: 8px;
  transition: color 0.2s;
}

.commentTime {
  color: #666;
  font-size: 0.9rem;
}

.commentContent {
  margin-left: 44px;
  color: #333;
}

.commentContent p {
  margin: 0 0 8px 0;
}

.commentImage {
  margin-top: 8px;
}

.commentImageFile {
  max-width: 300px;
  max-height: 200px;
  border-radius: 8px;
  object-fit: cover;
  cursor: pointer;
  transition: opacity 0.2s;
}

.commentImageFile:hover {
  opacity: 0.9;
}

.commentForm {
  padding: 16px;
  border-top: 1px solid #eee;
}

.commentInputContainer {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.commentTextarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: none;
  min-height: 40px;
  max-height: 120px;
  font-family: inherit;
}

.commentActions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.imageUploadButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #f0f2f5;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 16px;
}

.imageUploadButton:hover {
  background-color: #e4e6ea;
}

.submitButton {
  padding: 8px 16px;
  background-color: #1877f2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.2s;
}

.submitButton:hover:not(:disabled) {
  background-color: #166fe5;
}

.submitButton:disabled {
  background-color: #e4e6eb;
  cursor: not-allowed;
}

.imagePreview {
  position: relative;
  display: inline-block;
  margin-top: 8px;
}

.previewImage {
  max-width: 200px;
  max-height: 150px;
  border-radius: 8px;
  object-fit: cover;
}

.removeImageButton {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #ff4757;
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.removeImageButton:hover {
  background-color: #ff3838;
}

.error {
  color: #dc3545;
  padding: 8px;
  margin-bottom: 16px;
  background-color: #f8d7da;
  border-radius: 4px;
}

.noComments {
  text-align: center;
  color: #666;
  padding: 24px;
} 