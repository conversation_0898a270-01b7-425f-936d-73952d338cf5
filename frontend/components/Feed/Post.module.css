.post {
  padding: var(--spacing-lg) var(--spacing-xl);
}

.postHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
}

.postUser {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.postUserInfo h4 {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
  line-height: 1.25;
}

.postMeta {
  font-size: 12px;
  color: var(--text-muted);
  line-height: 1.33;
}

.postMenu {
  padding: var(--spacing-sm);
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.postMenu:hover {
  background-color: var(--card-hover);
}

.postContent {
  margin-bottom: var(--spacing-md);
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
}

.postImage {
  width: 100%;
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-md);
  background-color: var(--background);
  border: 1px solid var(--border);
  overflow: hidden;
  transition: all 0.15s ease-in-out;
  cursor: pointer;
  max-height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.postImage:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.postImage img {
  width: auto;
  height: auto;
  display: block;
  max-width: 100%;
  max-height: 500px;
  object-fit: contain;
  background: var(--white);
  transition: transform 0.2s ease;
  border-radius: 6px;
}

/* Ensure images don't get too small on very wide screens */
@media (min-width: 1200px) {
  .postImage img {
    min-width: 300px;
  }
}

.postImage:hover img {
  transform: scale(1.02);
}

.postStats {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm) 0;
  border-top: 1px solid var(--border);
  border-bottom: 1px solid var(--border);
  margin-bottom: var(--spacing-sm);
  font-size: 12px;
  color: var(--text-muted);
}

.postActionsRow {
  display: flex;
  justify-content: space-around;
}

.postAction {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 600;
  min-height: 40px;
  justify-content: center;
}

.postAction:hover {
  background-color: var(--card-hover);
  color: var(--primary-purple);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.postAction.liked {
  color: var(--primary-purple);
  background-color: var(--purple-gradient-light);
}

/* Responsive design */
@media (max-width: 768px) {
  .postImage {
    max-height: 400px;
  }

  .postImage img {
    max-height: 400px;
    max-width: 100%;
  }

  .postAction {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .postImage {
    max-height: 300px;
  }

  .postImage img {
    max-height: 300px;
    max-width: 100%;
  }

  .postAction {
    font-size: 12px;
  }
}

@media (max-width: 320px) {
  .postImage {
    max-height: 250px;
  }

  .postImage img {
    max-height: 250px;
  }
}
