.createPost {
  padding: var(--spacing-lg) var(--spacing-xl);
}

.createPostHeader {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.avatarImage {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.avatarText {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 18px;
  text-transform: uppercase;
}

.postInput {
  flex: 1;
  min-height: 100px;
  padding: var(--spacing-md);
  border: 2px solid var(--border);
  border-radius: var(--radius-lg);
  resize: none;
  font-family: inherit;
  font-size: 16px;
  background-color: var(--card);
  color: var(--text-primary);
  transition: all 0.15s ease-in-out;
}

.postInput:focus {
  outline: none;
  border-color: var(--primary-purple);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  transform: translateY(-1px);
}

.imagePreview {
  position: relative;
  margin: var(--spacing-lg) 0;
  max-width: 100%;
}

.imagePreview img {
  max-width: 100%;
  max-height: 300px;
  border-radius: var(--radius-md);
}

.removeImage {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: var(--radius-full);
  width: 28px;
  height: 28px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.15s ease-in-out;
}

.removeImage:hover {
  background: rgba(0, 0, 0, 0.9);
}

.errorMessage {
  color: var(--error);
  margin: var(--spacing-sm) 0;
  font-size: 14px;
}

.postActions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border);
}

.postOptions {
  display: flex;
  gap: var(--spacing-lg);
}

.postOption {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-lg);
  transition: all 0.15s ease-in-out;
  font-weight: 500;
}

.postOption:hover {
  background-color: var(--card-hover);
  color: var(--primary-purple);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.postSubmitArea {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.privacySelector select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--border);
  border-radius: var(--radius-lg);
  background-color: var(--card);
  color: var(--text-primary);
  cursor: pointer;
  font-family: inherit;
  font-size: 14px;
  transition: all 0.15s ease-in-out;
}

.privacySelector select:focus {
  outline: none;
  border-color: var(--primary-purple);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.privacySelector select:disabled {
  background-color: var(--background);
  color: var(--text-muted);
  cursor: not-allowed;
}
