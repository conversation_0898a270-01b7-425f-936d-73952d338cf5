.postList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem 0;
}

.postCard {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1rem;
}

.postHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.postPrivacyIcon {
  margin-right: 0.5rem;
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.userDetails {
  display: flex;
  flex-direction: column;
}

.avatarLink {
  text-decoration: none;
  transition: opacity 0.2s;
}

.avatarLink:hover {
  opacity: 0.8;
}

.userNameLink {
  text-decoration: none;
  color: inherit;
}

.userNameLink:hover .userName {
  color: var(--primary-purple, #8b5cf6);
}

.userName {
  font-weight: 600;
  color: #1a1a1a;
  transition: color 0.2s;
}

.postTime {
  font-size: 0.875rem;
  color: #666;
}

.postPrivacy {
  color: #666;
}

.postMenu {
  position: relative;
}

.menuButton {
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  padding: 0.5rem;
  border-radius: 50%;
}

.menuButton:hover {
  background-color: #f5f5f5;
}

.dropdownMenu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  z-index: 10;
}

.dropdownMenu button {
  border: none;
  background: none;
  text-align: left;
  padding: 0.5rem 1rem;
  cursor: pointer;
  border-radius: 4px;
  width: 100%;
}

.dropdownMenu button:hover {
  background-color: #f5f5f5;
}

.postContent {
  margin-bottom: 1rem;
  white-space: pre-wrap;
  word-break: break-word;
}

.postImage {
  margin-bottom: 1rem;
  border-radius: 8px;
  overflow: hidden;
  max-height: 500px;
  display: flex;
  justify-content: center;
  background-color: #f0f2f5;
}

.postImage img,
.postImage video {
  max-width: 100%;
  max-height: 500px;
  width: auto;
  height: auto;
  display: block;
}

.postActions {
  display: flex;
  justify-content: space-around;
  gap: 1rem;
  padding-top: 0.75rem;
  border-top: 1px solid #eee;
}

.actionButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border: none;
  background: none;
  color: #666;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.actionButton:hover {
  background-color: #f5f5f5;
}

.actionButton.liked {
  color: #e74c3c;
}

.actionButton.liked i {
  color: #e74c3c;
}

.loading {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.error {
  text-align: center;
  padding: 2rem;
  color: #dc3545;
}

.noPosts {
  text-align: center;
  padding: 2rem;
  color: #666;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.editPost {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.editTextArea {
  width: 100%;
  min-height: 80px;
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid #ddd;
  font-family: inherit;
  font-size: 1rem;
}

.editActions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.editActions button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.cancelButton {
  background-color: #e4e6eb;
  color: #1a1a1a;
}

.saveButton {
  background-color: #1877f2;
  color: white;
}

.toast {
  position: fixed;
  bottom: 20px;
  right: 20px;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  animation: slideInUp 0.3s ease-out;
  font-weight: 500;
  font-size: 14px;
}

.toastSuccess {
  background-color: var(--primary-purple, #8b5cf6);
}

.toastError {
  background-color: #e74c3c;
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
} 