.eventList {
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: var(--shadow);
  overflow: hidden;
}

.eventListHeader {
  padding: 20px;
  border-bottom: 1px solid var(--border-gray);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.eventListHeader h3 {
  margin: 0;
  color: var(--primary-navy);
  font-size: 18px;
  font-weight: 600;
}

.eventStats {
  display: flex;
  gap: 15px;
  font-size: 12px;
}

.upcomingCount {
  color: #27ae60;
  font-weight: 600;
}

.pastCount {
  color: var(--text-light);
  font-weight: 600;
}

.eventsContainer {
  padding: 20px;
}

.eventSection {
  margin-bottom: 30px;
}

.eventSection:last-child {
  margin-bottom: 0;
}

.sectionTitle {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 15px 0;
  color: var(--primary-navy);
  font-size: 16px;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 2px solid var(--border-gray);
}

.sectionTitle i {
  color: var(--text-light);
}

.loadMoreContainer {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--border-gray);
}

.loadMoreContainer button {
  padding: 10px 20px;
  font-size: 14px;
}

.loadingState,
.errorState,
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: var(--text-light);
  gap: 15px;
}

.loadingState i,
.errorState i,
.emptyState i {
  font-size: 48px;
  color: var(--border-gray);
  margin-bottom: 10px;
}

.errorState i {
  color: #e74c3c;
}

.errorState p,
.emptyState p {
  margin: 0;
  font-weight: 500;
  color: var(--primary-navy);
  font-size: 16px;
}

.emptyState span {
  margin: 0;
  font-size: 14px;
}

.errorState button {
  margin-top: 10px;
  padding: 8px 16px;
  font-size: 14px;
}

@media (max-width: 768px) {
  .eventListHeader {
    padding: 15px;
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .eventsContainer {
    padding: 15px;
  }
  
  .eventStats {
    gap: 10px;
  }
  
  .sectionTitle {
    font-size: 14px;
  }
  
  .loadingState,
  .errorState,
  .emptyState {
    padding: 40px 20px;
  }
  
  .loadingState i,
  .errorState i,
  .emptyState i {
    font-size: 36px;
  }
}