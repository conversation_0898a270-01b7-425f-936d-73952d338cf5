.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modalContent {
  background-color: #fff !important;
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-heavy, 0 8px 32px rgba(60, 60, 100, 0.18));
  border: 1px solid var(--border-gray, #e0e0e0);
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 15px;
  border-bottom: 1px solid var(--border-gray);
}

.modalHeader h2 {
  margin: 0;
  color: var(--primary-navy);
  font-size: 18px;
  font-weight: 600;
}

.closeButton {
  background: none;
  border: none;
  font-size: 16px;
  color: var(--text-light);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.closeButton:hover {
  background-color: var(--light-gray);
}

.closeButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.groupInfo {
  padding: 15px 20px;
  background-color: var(--light-gray);
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--text-dark);
}

.groupInfo i {
  color: var(--primary-navy);
}

.eventForm {
  background: #fff !important;
  border-radius: 8px;
  padding: 20px;
}

.formGroup {
  margin-bottom: 24px;
}

.formGroup label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #7b3ff2;
  letter-spacing: 0.02em;
}

.formGroup input,
.formGroup textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1.5px solid #c3c8d4;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
  background: #f7f8fa !important;
  color: #222;
  box-shadow: none;
}

.formGroup input:focus,
.formGroup textarea:focus {
  outline: none;
  border-color: #7b3ff2;
  box-shadow: 0 0 0 2px #e5d8fd;
  background: #fff !important;
}

.formGroup input.error,
.formGroup textarea.error {
  border-color: #e74c3c;
}

.formGroup textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.errorMessage {
  color: #e74c3c;
  font-size: 12px;
  margin-top: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.charCount {
  font-size: 11px;
  color: var(--text-light);
  text-align: right;
  margin-top: 4px;
}

.responseOptions {
  display: flex;
  gap: 20px;
  margin-top: 8px;
}

.radioOption {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  transition: all 0.2s;
  flex: 1;
}

.radioOption:hover {
  border-color: #7b3ff2;
  background-color: #f8f6ff;
}

.radioOption input[type="radio"] {
  display: none;
}

.radioOption input[type="radio"]:checked + .radioLabel {
  color: #7b3ff2;
  font-weight: 600;
}

.radioOption:has(input[type="radio"]:checked) {
  border-color: #7b3ff2;
  background-color: #f8f6ff;
}

.radioLabel {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
  transition: color 0.2s;
  width: 100%;
  justify-content: center;
}

.radioLabel i {
  font-size: 16px;
}

.dateTimeRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.submitError {
  background-color: #fee;
  color: #e74c3c;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.modalActions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  padding-top: 15px;
  border-top: 1px solid var(--border-gray);
}

.modalActions button {
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
}

@media (max-width: 768px) {
  .modalOverlay {
    padding: 10px;
  }
  
  .modalContent {
    max-height: 95vh;
  }
  
  .modalHeader {
    padding: 15px 15px 10px;
  }
  
  .modalHeader h2 {
    font-size: 16px;
  }
  
  .eventForm {
    padding: 15px;
  }
  
  .dateTimeRow {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .responseOptions {
    flex-direction: column;
    gap: 12px;
  }
  
  .modalActions {
    flex-direction: column-reverse;
  }
  
  .modalActions button {
    width: 100%;
  }
}