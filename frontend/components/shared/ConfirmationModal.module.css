.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
  backdrop-filter: blur(3px);
  animation: fadeIn 0.3s ease-out;
}

.modalContent {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: 450px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  animation: slideUp 0.3s ease-out;
  overflow: hidden;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid #e5e7eb;
}

.modalTitle {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.closeButton {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  font-size: 16px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeButton:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.modalBody {
  padding: 24px;
}

.modalMessage {
  margin: 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 1rem;
}

.modalActions {
  display: flex;
  gap: 12px;
  padding: 16px 24px 24px 24px;
  justify-content: flex-end;
}

.cancelButton {
  background: #f9fafb;
  border: 1px solid #d1d5db;
  color: #374151;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  font-size: 14px;
}

.cancelButton:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.confirmButton {
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  font-size: 14px;
  border: none;
  color: white;
}

.confirmButton.default {
  background: #3b82f6;
}

.confirmButton.default:hover {
  background: #2563eb;
}

.confirmButton.danger {
  background: #ef4444;
}

.confirmButton.danger:hover {
  background: #dc2626;
}

.confirmButton.warning {
  background: #f59e0b;
}

.confirmButton.warning:hover {
  background: #d97706;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive design */
@media (max-width: 480px) {
  .modalOverlay {
    padding: 16px;
  }
  
  .modalContent {
    max-width: 100%;
  }
  
  .modalHeader {
    padding: 20px 20px 12px 20px;
  }
  
  .modalBody {
    padding: 20px;
  }
  
  .modalActions {
    padding: 12px 20px 20px 20px;
    flex-direction: column-reverse;
  }
  
  .cancelButton,
  .confirmButton {
    width: 100%;
    justify-content: center;
  }
}
