.avatar {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
  position: relative;
}

.avatarImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.avatarFallback {
  background-color: var(--primary-purple, #8b5cf6);
  color: white;
  font-weight: 600;
}

.avatarIcon {
  background-color: var(--background-secondary, #f3f4f6);
  color: var(--text-muted, #6b7280);
}

.avatarInitials {
  font-size: inherit;
  font-weight: inherit;
  line-height: 1;
}

/* Size variants */
.small {
  width: 32px;
  height: 32px;
  font-size: 12px;
}

.medium {
  width: 40px;
  height: 40px;
  font-size: 14px;
}

.large {
  width: 48px;
  height: 48px;
  font-size: 16px;
}

.xlarge {
  width: 120px;
  height: 120px;
  font-size: 32px;
}