.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
  cursor: pointer;
}

.modalContent {
  position: relative;
  max-width: 95vw;
  max-height: 95vh;
  cursor: default;
}

.closeButton {
  position: absolute;
  top: -3rem;
  right: 0;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;
  backdrop-filter: blur(10px);
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.modalImage {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

/* Responsive design */
@media (max-width: 768px) {
  .modalOverlay {
    padding: 1rem;
  }
  
  .closeButton {
    top: -2.5rem;
    width: 36px;
    height: 36px;
    font-size: 14px;
  }
  
  .modalContent {
    max-width: 100vw;
    max-height: 100vh;
  }
}

@media (max-width: 480px) {
  .modalOverlay {
    padding: 0.5rem;
  }
  
  .closeButton {
    top: -2rem;
    right: -0.5rem;
    width: 32px;
    height: 32px;
    font-size: 12px;
  }
}
