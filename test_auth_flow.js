// Test script to verify the authentication flow fixes
const fetch = require('node-fetch');

const API_BASE_URL = 'http://localhost:8000';
const CLIENT_IDENTIFIER = 'electron-desktop';

async function testAuthFlow() {
    console.log('Testing authentication flow...\n');

    // Test 1: Check if server is responding
    console.log('1. Testing server connectivity...');
    try {
        const response = await fetch(`${API_BASE_URL}/api/auth/profile`);
        const data = await response.json();
        console.log('✓ Server is responding');
        console.log('Response:', data);
        console.log('Status:', response.status);
    } catch (error) {
        console.log('✗ Server connectivity failed:', error.message);
        return;
    }

    // Test 2: Test login with x-client header
    console.log('\n2. Testing login with x-client header...');
    try {
        const loginResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'x-client': CLIENT_IDENTIFIER,
            },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'testpassword'
            }),
            credentials: 'include'
        });

        const loginData = await loginResponse.json();
        console.log('Login response status:', loginResponse.status);
        console.log('Login response:', loginData);

        if (loginResponse.status === 401 || loginResponse.status === 400) {
            console.log('✓ Login endpoint is working (expected auth failure for test credentials)');
        } else if (loginResponse.ok) {
            console.log('✓ Login successful (unexpected but good!)');
        } else {
            console.log('? Unexpected login response');
        }
    } catch (error) {
        console.log('✗ Login test failed:', error.message);
    }

    // Test 3: Test CORS headers
    console.log('\n3. Testing CORS headers...');
    try {
        const corsResponse = await fetch(`${API_BASE_URL}/api/auth/profile`, {
            method: 'OPTIONS',
            headers: {
                'Origin': 'file://',
                'Access-Control-Request-Headers': 'Content-Type, x-client'
            }
        });

        console.log('CORS preflight status:', corsResponse.status);
        console.log('Access-Control-Allow-Headers:', corsResponse.headers.get('Access-Control-Allow-Headers'));
        console.log('Access-Control-Allow-Origin:', corsResponse.headers.get('Access-Control-Allow-Origin'));

        if (corsResponse.headers.get('Access-Control-Allow-Headers')?.includes('x-client')) {
            console.log('✓ x-client header is allowed in CORS');
        } else {
            console.log('✗ x-client header is not allowed in CORS');
        }
    } catch (error) {
        console.log('✗ CORS test failed:', error.message);
    }

    console.log('\n=== Test Summary ===');
    console.log('The authentication flow has been updated with:');
    console.log('- Correct port configuration (8000)');
    console.log('- x-client header support');
    console.log('- Enhanced error handling and logging');
    console.log('- Centralized configuration management');
}

testAuthFlow().catch(console.error);
