services:
  backend:
    build: ./backend
    ports:
      - "8000:8000"
    volumes:
      - ./backend/data:/root/data
      - ./backend/uploads:/root/uploads
    environment:
      - DATABASE_PATH=/root/data/ripple.db
      - UPLOADS_PATH=/root/uploads
      - FRONTEND_URL=http://localhost:3000
    networks:
      - ripple-network

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8080
    depends_on:
      - backend
    networks:
      - ripple-network

networks:
  ripple-network:
    driver: bridge