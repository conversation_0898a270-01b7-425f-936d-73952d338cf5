# backend/.env.example
# Database Configuration
DATABASE_PATH=./data/ripple.db
MIGRATIONS_PATH=./pkg/db/migrations/sqlite

# Server Configuration
SERVER_PORT=8000
FRONTEND_URL=http://localhost:3000

# Session Configuration
SESSION_SECRET=your-super-secret-key-change-this-in-production

# File Upload Configuration
UPLOADS_PATH=./uploads
MAX_FILE_SIZE=20971520

# WebSocket Configuration (optional)
WEBSOCKET_READ_BUFFER_SIZE=1024
WEBSOCKET_WRITE_BUFFER_SIZE=1024
WEBSOCKET_MAX_MESSAGE_SIZE=2048

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Development Configuration
LOG_LEVEL=info
DEBUG_MODE=true