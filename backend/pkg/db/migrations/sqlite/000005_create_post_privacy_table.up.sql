-- backend/pkg/db/migrations/sqlite/000005_create_post_privacy_table.up.sql
CREATE TABLE post_privacy (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    post_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREI<PERSON><PERSON> KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE,
    FOREI<PERSON><PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(post_id, user_id)
);