* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: #f0f2f5;
    height: 100vh;
    overflow: hidden;
}

.screen {
    height: 100vh;
    display: flex;
}

.hidden {
    display: none !important;
}

/* Login Screen */
#login-screen {
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-container {
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    width: 400px;
    text-align: center;
}

.logo h1 {
    color: #667eea;
    margin-bottom: 30px;
    font-size: 28px;
}

#login-form input {
    width: 100%;
    padding: 12px;
    margin: 8px 0;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

#login-form button {
    width: 100%;
    padding: 12px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
    margin-top: 16px;
}

#login-form button:hover {
    background: #5a6fd8;
}

.register-link {
    margin-top: 20px;
    color: #666;
}

.register-link a {
    color: #667eea;
    text-decoration: none;
}

.error-message {
    color: #e74c3c;
    margin-top: 10px;
    font-size: 14px;
}

/* Chat Layout */
.chat-layout {
    display: flex;
    width: 100%;
    height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 320px;
    background: white;
    border-right: 1px solid #e4e6ea;
    display: flex;
    flex-direction: column;
}

.user-info {
    padding: 16px;
    border-bottom: 1px solid #e4e6ea;
    display: flex;
    align-items: center;
    gap: 12px;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.user-details {
    flex: 1;
}

.user-details span {
    font-weight: 600;
    display: block;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-top: 4px;
}

.status-indicator.online {
    background: #42b883;
}

.status-indicator.offline {
    background: #95a5a6;
}

.logout-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
}

.logout-btn:hover {
    background: #f0f2f5;
}

.search-container {
    padding: 16px;
    border-bottom: 1px solid #e4e6ea;
}

#search-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 20px;
    background: #f0f2f5;
}

.contacts-header {
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e4e6ea;
}

.contacts-header h3 {
    font-size: 16px;
    color: #1c1e21;
}

.online-count {
    font-size: 12px;
    color: #65676b;
}

.contacts-list {
    flex: 1;
    overflow-y: auto;
}

.contact-item {
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    border-bottom: 1px solid #f0f2f5;
}

.contact-item:hover {
    background: #f0f2f5;
}

.contact-item.active {
    background: #e3f2fd;
}

.contact-info {
    flex: 1;
}

.contact-name {
    font-weight: 600;
    font-size: 14px;
}

.contact-status {
    font-size: 12px;
    color: #65676b;
}

.unread-count {
    background: #667eea;
    color: white;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 11px;
    min-width: 18px;
    text-align: center;
}

/* Chat Area */
.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;
}

.chat-header {
    padding: 16px;
    background: white;
    border-bottom: 1px solid #e4e6ea;
    display: flex;
    align-items: center;
    gap: 12px;
}

.chat-info span {
    font-weight: 600;
    display: block;
}

.chat-status {
    font-size: 12px;
    color: #65676b;
}

.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    display: flex;
    flex-direction: column;
}

.welcome-message {
    text-align: center;
    margin-top: 100px;
    color: #65676b;
}

.message {
    max-width: 70%;
    margin: 4px 0;
    padding: 8px 12px;
    border-radius: 18px;
    word-wrap: break-word;
}

.message.sent {
    align-self: flex-end;
    background: #667eea;
    color: white;
}

.message.received {
    align-self: flex-start;
    background: white;
    border: 1px solid #e4e6ea;
}

.message-time {
    font-size: 11px;
    opacity: 0.7;
    margin-top: 4px;
}

.typing-indicator {
    padding: 8px 16px;
    font-size: 12px;
    color: #65676b;
    font-style: italic;
}

.message-input-container {
    padding: 16px;
    background: white;
    border-top: 1px solid #e4e6ea;
}

.input-area {
    display: flex;
    align-items: center;
    gap: 8px;
}

.emoji-btn {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
}

.emoji-btn:hover {
    background: #f0f2f5;
}

#message-input {
    flex: 1;
    padding: 10px 16px;
    border: 1px solid #ddd;
    border-radius: 20px;
    outline: none;
}

.send-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 20px;
    cursor: pointer;
}

.send-btn:hover {
    background: #5a6fd8;
}

.send-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* Offline Indicator */
.offline-indicator {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: #e74c3c;
    color: white;
    text-align: center;
    padding: 8px;
    z-index: 1000;
}

/* Emoji Picker */
.emoji-picker {
    position: absolute;
    bottom: 80px;
    left: 16px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    z-index: 100;
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 8px;
}

.emoji {
    font-size: 20px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    text-align: center;
}

.emoji:hover {
    background: #f0f2f5;
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}