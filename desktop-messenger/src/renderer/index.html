<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ripple Messenger</title>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <div id="app">
        <!-- Login Screen -->
        <div id="login-screen" class="screen">
            <div class="login-container">
                <div class="logo">
                    <h1>Ripple Messenger</h1>
                </div>
                <form id="login-form">
                    <input type="email" id="email" placeholder="Email" required>
                    <input type="password" id="password" placeholder="Password" required>
                    <button type="submit">Login</button>
                </form>
                <p class="register-link">
                    Don't have an account? 
                    <a href="#" id="register-link">Register on our website</a>
                </p>
                <div id="login-error" class="error-message"></div>
            </div>
        </div>

        <!-- Main Chat Screen -->
        <div id="chat-screen" class="screen hidden">
            <div class="chat-layout">
                <!-- Sidebar -->
                <div class="sidebar">
                    <div class="user-info">
                        <img id="user-avatar" src="" alt="Avatar" class="avatar">
                        <div class="user-details">
                            <span id="user-name"></span>
                            <div class="status-indicator online" id="user-status"></div>
                        </div>
                        <button id="logout-btn" class="logout-btn">⚙️</button>
                    </div>
                    
                    <div class="search-container">
                        <input type="text" id="search-input" placeholder="Search messages...">
                    </div>
                    
                    <div class="contacts-header">
                        <h3>Contacts</h3>
                        <div class="online-count">
                            <span id="online-count">0</span> online
                        </div>
                    </div>
                    
                    <div id="contacts-list" class="contacts-list"></div>
                </div>

                <!-- Chat Area -->
                <div class="chat-area">
                    <div id="chat-header" class="chat-header hidden">
                        <img id="chat-avatar" src="" alt="Avatar" class="avatar">
                        <div class="chat-info">
                            <span id="chat-name"></span>
                            <div class="chat-status" id="chat-status"></div>
                        </div>
                    </div>
                    
                    <div id="messages-container" class="messages-container">
                        <div class="welcome-message">
                            <h2>Welcome to Ripple Messenger</h2>
                            <p>Select a contact to start chatting</p>
                        </div>
                    </div>
                    
                    <div id="message-input-container" class="message-input-container hidden">
                        <div class="typing-indicator" id="typing-indicator"></div>
                        <div class="input-area">
                            <button id="emoji-btn" class="emoji-btn">😊</button>
                            <input type="text" id="message-input" placeholder="Type a message...">
                            <button id="send-btn" class="send-btn">Send</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Offline Indicator -->
        <div id="offline-indicator" class="offline-indicator hidden">
            <span>⚠️ You are offline</span>
        </div>

        <!-- Emoji Picker -->
        <div id="emoji-picker" class="emoji-picker hidden">
            <div class="emoji-grid">
                <span class="emoji">😀</span><span class="emoji">😃</span><span class="emoji">😄</span><span class="emoji">😁</span>
                <span class="emoji">😊</span><span class="emoji">😍</span><span class="emoji">🥰</span><span class="emoji">😘</span>
                <span class="emoji">😂</span><span class="emoji">🤣</span><span class="emoji">😭</span><span class="emoji">😢</span>
                <span class="emoji">😤</span><span class="emoji">😠</span><span class="emoji">🤔</span><span class="emoji">😎</span>
                <span class="emoji">👍</span><span class="emoji">👎</span><span class="emoji">👏</span><span class="emoji">🙌</span>
                <span class="emoji">❤️</span><span class="emoji">💔</span><span class="emoji">🔥</span><span class="emoji">⭐</span>
            </div>
        </div>
    </div>

    <script src="js/config.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/websocket.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/chat.js"></script>
    <script src="js/app.js"></script>
</body>
</html>