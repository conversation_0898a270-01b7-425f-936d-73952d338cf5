{"name": "ripple-messenger", "version": "1.0.0", "description": "Cross-platform desktop messenger for Ripple Social Network", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux"}, "build": {"appId": "com.ripple.messenger", "productName": "<PERSON><PERSON><PERSON> Messenger", "directories": {"output": "dist"}, "files": ["src/**/*", "node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}, "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"ws": "^8.14.2", "electron-store": "^8.1.0", "node-notifier": "^10.0.1"}}